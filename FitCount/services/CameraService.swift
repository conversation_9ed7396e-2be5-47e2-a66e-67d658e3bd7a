import Foundation
import AVFoundation
import Combine
import UIKit

// MARK: - 相机服务实现类

/// 相机服务实现
/// 负责管理相机会话、权限处理、设备切换等核心功能
final class CameraService: NSObject, CameraServiceProtocol {

    // MARK: - 发布属性

    /// 相机会话状态
    @Published private(set) var sessionState: CameraSessionState = .notInitialized

    /// 当前使用的相机位置
    @Published private(set) var currentPosition: CameraPosition = .back

    /// 相机预览层
    @Published private(set) var previewLayer: AVCaptureVideoPreviewLayer?

    /// 错误信息
    @Published private(set) var errorMessage: String?

    // MARK: - 私有属性

    /// AVCaptureSession实例
    private let session = AVCaptureSession()

    /// 视频数据输出
    private let videoOutput = AVCaptureVideoDataOutput()

    /// 相机配置参数
    private var configuration: CameraConfiguration = .default

    /// 会话队列，用于异步处理相机操作
    private let sessionQueue = DispatchQueue(label: "com.fitcount.camera.session", qos: .userInitiated)

    /// 视频输出队列
    private let videoOutputQueue = DispatchQueue(label: "com.fitcount.camera.videoOutput", qos: .userInitiated)

    /// 通知中心
    private let notificationCenter = NotificationCenter.default

    /// Combine取消令牌集合
    private var cancellables = Set<AnyCancellable>()

    /// 数据输出代理
    weak var dataOutputDelegate: CameraDataOutputDelegate?

    /// 状态观察者集合
    private var stateObservers: [WeakReference<CameraStateObserver>] = []

    // MARK: - 初始化

    override init() {
        super.init()
        DebugLogger.info("CameraService初始化")
        setupNotificationObservers()
    }

    deinit {
        DebugLogger.info("CameraService销毁")
        removeNotificationObservers()
        stopSession()
    }

    // MARK: - CameraServiceProtocol实现

    /// 设置并启动相机会话
    /// - Parameter configuration: 相机配置参数
    /// - Returns: 配置结果的发布者
    func setupAndStartSession(with configuration: CameraConfiguration = .default) -> AnyPublisher<CameraConfigurationStatus, Never> {
        DebugLogger.info("开始设置并启动相机会话")

        self.configuration = configuration

        return Future<CameraConfigurationStatus, Never> { [weak self] promise in
            guard let self = self else {
                promise(.success(.configurationFailed(CameraError.unknown(NSError(domain: "CameraService", code: -1, userInfo: [NSLocalizedDescriptionKey: "服务实例已释放"])))))
                return
            }

            self.sessionQueue.async {
                self.updateSessionState(.configuring)

                // 检查相机权限
                let authStatus = self.checkCameraPermission()
                switch authStatus {
                case .authorized:
                    let result = self.configureSession()
                    promise(.success(result))

                case .notDetermined:
                    // 请求权限
                    self.requestCameraPermission()
                        .sink { granted in
                            if granted {
                                let result = self.configureSession()
                                promise(.success(result))
                            } else {
                                promise(.success(.permissionDenied))
                            }
                        }
                        .store(in: &self.cancellables)

                case .denied:
                    promise(.success(.permissionDenied))

                case .restricted:
                    promise(.success(.permissionRestricted))

                @unknown default:
                    promise(.success(.configurationFailed(CameraError.unknown(NSError(domain: "CameraService", code: -2, userInfo: [NSLocalizedDescriptionKey: "未知权限状态"])))))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    /// 启动相机会话
    /// - Returns: 启动结果的发布者
    func startSession() -> AnyPublisher<Bool, Never> {
        DebugLogger.info("启动相机会话")

        return Future<Bool, Never> { [weak self] promise in
            guard let self = self else {
                promise(.success(false))
                return
            }

            self.sessionQueue.async {
                if self.session.isRunning {
                    DebugLogger.warning("会话已经在运行中")
                    promise(.success(true))
                    return
                }

                if case .interrupted = self.sessionState {
                    DebugLogger.warning("会话被中断，无法启动")
                    promise(.success(false))
                    return
                }

                self.session.startRunning()
                let isRunning = self.session.isRunning

                DispatchQueue.main.async {
                    if isRunning {
                        self.updateSessionState(.running)
                    } else {
                        self.updateSessionState(.error(CameraError.sessionStartFailed))
                    }
                }

                promise(.success(isRunning))
            }
        }
        .eraseToAnyPublisher()
    }

    /// 停止相机会话
    func stopSession() {
        DebugLogger.info("停止相机会话")

        sessionQueue.async { [weak self] in
            guard let self = self else { return }

            if self.session.isRunning {
                self.session.stopRunning()
            }

            DispatchQueue.main.async {
                self.updateSessionState(.stopped)
            }
        }
    }

    /// 切换前后摄像头
    /// - Returns: 切换结果的发布者
    func toggleCamera() -> AnyPublisher<CameraPosition, CameraError> {
        DebugLogger.info("切换前后摄像头")

        return Future<CameraPosition, CameraError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.unknown(NSError(domain: "CameraService", code: -3, userInfo: [NSLocalizedDescriptionKey: "服务实例已释放"]))))
                return
            }

            self.sessionQueue.async {
                let newPosition = self.currentPosition.opposite

                // 检查新位置的相机是否可用
                guard self.isCameraSupported(at: newPosition) else {
                    promise(.failure(.deviceNotAvailable))
                    return
                }

                self.session.beginConfiguration()

                // 移除当前输入
                if let currentInput = self.session.inputs.first as? AVCaptureDeviceInput {
                    self.session.removeInput(currentInput)
                }

                // 添加新输入
                do {
                    guard let newDevice = self.getCameraDevice(for: newPosition) else {
                        self.session.commitConfiguration()
                        promise(.failure(.deviceNotAvailable))
                        return
                    }

                    let newInput = try AVCaptureDeviceInput(device: newDevice)

                    if self.session.canAddInput(newInput) {
                        self.session.addInput(newInput)
                        self.session.commitConfiguration()

                        DispatchQueue.main.async {
                            self.currentPosition = newPosition
                            self.notifyStateObservers { observer in
                                observer.cameraPositionDidChange(from: self.currentPosition.opposite, to: newPosition)
                            }
                        }

                        promise(.success(newPosition))
                    } else {
                        self.session.commitConfiguration()
                        promise(.failure(.sessionConfigurationFailed))
                    }
                } catch {
                    self.session.commitConfiguration()
                    promise(.failure(.inputCreationFailed(error)))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    /// 检查相机权限状态
    /// - Returns: 权限状态
    func checkCameraPermission() -> AVAuthorizationStatus {
        return AVCaptureDevice.authorizationStatus(for: .video)
    }

    /// 请求相机权限
    /// - Returns: 权限请求结果的发布者
    func requestCameraPermission() -> AnyPublisher<Bool, Never> {
        return Future<Bool, Never> { promise in
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    promise(.success(granted))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 状态观察者管理

    /// 添加状态观察者
    /// - Parameter observer: 状态观察者
    func addStateObserver(_ observer: CameraStateObserver) {
        stateObservers.append(WeakReference(observer))
        cleanupObservers()
    }

    /// 移除状态观察者
    /// - Parameter observer: 要移除的观察者
    func removeStateObserver(_ observer: CameraStateObserver) {
        stateObservers.removeAll { $0.value === observer }
    }

    // MARK: - 私有方法

    /// 配置相机会话
    /// - Returns: 配置结果
    private func configureSession() -> CameraConfigurationStatus {
        DebugLogger.debug("开始配置相机会话")

        session.beginConfiguration()

        // 设置会话预设
        if session.canSetSessionPreset(configuration.sessionPreset) {
            session.sessionPreset = configuration.sessionPreset
        }

        // 清除现有输入和输出
        session.inputs.forEach { session.removeInput($0) }
        session.outputs.forEach { session.removeOutput($0) }

        // 配置视频输入
        guard let videoDevice = getCameraDevice(for: configuration.position) else {
            session.commitConfiguration()
            return .deviceNotAvailable
        }

        do {
            let videoInput = try AVCaptureDeviceInput(device: videoDevice)

            if session.canAddInput(videoInput) {
                session.addInput(videoInput)
            } else {
                session.commitConfiguration()
                return .configurationFailed(CameraError.sessionConfigurationFailed)
            }
        } catch {
            session.commitConfiguration()
            return .configurationFailed(CameraError.inputCreationFailed(error))
        }

        // 配置视频输出
        if session.canAddOutput(videoOutput) {
            session.addOutput(videoOutput)

            videoOutput.videoSettings = configuration.videoSettings
            videoOutput.alwaysDiscardsLateVideoFrames = configuration.alwaysDiscardsLateVideoFrames
            videoOutput.setSampleBufferDelegate(self, queue: videoOutputQueue)
        } else {
            session.commitConfiguration()
            return .configurationFailed(CameraError.outputConfigurationFailed)
        }

        // 创建预览层
        let newPreviewLayer = AVCaptureVideoPreviewLayer(session: session)
        newPreviewLayer.videoGravity = configuration.videoGravity

        session.commitConfiguration()

        DispatchQueue.main.async { [weak self] in
            self?.previewLayer = newPreviewLayer
            self?.currentPosition = self?.configuration.position ?? .back
            self?.updateSessionState(.configured)
        }

        return .success
    }

    /// 更新会话状态
    /// - Parameter newState: 新状态
    private func updateSessionState(_ newState: CameraSessionState) {
        let oldState = sessionState
        sessionState = newState

        // 更新错误信息
        if case .error(let error) = newState {
            errorMessage = error.localizedDescription
        } else {
            errorMessage = nil
        }

        // 通知观察者
        notifyStateObservers { observer in
            observer.cameraStateDidChange(from: oldState, to: newState)
        }

        DebugLogger.debug("相机状态变化: \(oldState.description) -> \(newState.description)")
    }

    /// 通知状态观察者
    /// - Parameter action: 通知动作
    private func notifyStateObservers(_ action: (CameraStateObserver) -> Void) {
        cleanupObservers()
        stateObservers.compactMap { $0.value }.forEach(action)
    }

    /// 清理已释放的观察者
    private func cleanupObservers() {
        stateObservers.removeAll { $0.value == nil }
    }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate

extension CameraService: AVCaptureVideoDataOutputSampleBufferDelegate {

    /// 相机输出视频帧回调
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        guard CMSampleBufferGetImageBuffer(sampleBuffer) != nil else { return }

        let orientation = UIImage.Orientation.from(deviceOrientation: UIDevice.current.orientation)

        // 通知数据输出代理
        DispatchQueue.main.async { [weak self] in
            self?.dataOutputDelegate?.cameraDidOutput(sampleBuffer: sampleBuffer, orientation: orientation)
        }
    }
}

// MARK: - 通知观察者管理

extension CameraService {

    /// 设置通知观察者
    private func setupNotificationObservers() {
        DebugLogger.debug("设置相机会话通知观察者")

        // 监听会话被中断
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionWasInterrupted),
            name: .AVCaptureSessionWasInterrupted,
            object: session
        )

        // 监听会话中断结束
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionInterruptionEnded),
            name: .AVCaptureSessionInterruptionEnded,
            object: session
        )

        // 监听会话运行错误
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionRuntimeError),
            name: .AVCaptureSessionRuntimeError,
            object: session
        )

        // 监听会话开始运行
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionDidStartRunning),
            name: .AVCaptureSessionDidStartRunning,
            object: session
        )

        // 监听会话停止运行
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionDidStopRunning),
            name: .AVCaptureSessionDidStopRunning,
            object: session
        )

        // 监听应用进入后台
        notificationCenter.addObserver(
            self,
            selector: #selector(applicationWillResignActive),
            name: UIScene.willDeactivateNotification,
            object: nil
        )

        // 监听应用回到前台
        notificationCenter.addObserver(
            self,
            selector: #selector(applicationDidBecomeActive),
            name: UIScene.didActivateNotification,
            object: nil
        )
    }

    /// 移除通知观察者
    private func removeNotificationObservers() {
        DebugLogger.debug("移除相机会话通知观察者")
        notificationCenter.removeObserver(self)
    }

    // MARK: - 通知处理方法

    /// 相机会话被中断
    @objc private func sessionWasInterrupted(notification: NSNotification) {
        DebugLogger.warning("相机会话被中断")

        var reason = "未知原因"

        if let userInfoValue = notification.userInfo?[AVCaptureSessionInterruptionReasonKey] as AnyObject?,
           let reasonIntegerValue = userInfoValue.integerValue,
           let interruptionReason = AVCaptureSession.InterruptionReason(rawValue: reasonIntegerValue) {

            switch interruptionReason {
            case .videoDeviceNotAvailableWithMultipleForegroundApps:
                reason = "多个前台应用使用摄像头"
            case .videoDeviceInUseByAnotherClient:
                reason = "摄像头被其他应用使用"
            case .videoDeviceNotAvailableDueToSystemPressure:
                reason = "系统压力导致摄像头不可用"
            case .audioDeviceInUseByAnotherClient:
                reason = "音频设备被其他应用使用"
            case .videoDeviceNotAvailableInBackground:
                reason = "应用在后台时摄像头不可用"
            @unknown default:
                reason = "未知原因(\(interruptionReason.rawValue))"
            }
        }

        DispatchQueue.main.async { [weak self] in
            self?.updateSessionState(.interrupted(reason: reason))
            self?.dataOutputDelegate?.cameraSessionWasInterrupted(reason: reason)
        }
    }

    /// 相机会话中断结束
    @objc private func sessionInterruptionEnded(notification: NSNotification) {
        DebugLogger.info("相机会话中断结束")

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 如果会话不在运行，尝试重新启动
            if !self.session.isRunning {
                self.startSession()
                    .sink { _ in }
                    .store(in: &self.cancellables)
            } else {
                self.updateSessionState(.running)
            }

            self.dataOutputDelegate?.cameraSessionInterruptionEnded()
        }
    }

    /// 相机会话运行错误
    @objc private func sessionRuntimeError(notification: NSNotification) {
        guard let error = notification.userInfo?[AVCaptureSessionErrorKey] as? AVError else {
            DebugLogger.error("相机会话运行错误，但无法获取错误详情")
            return
        }

        DebugLogger.error("相机会话运行错误: \(error.localizedDescription)")

        DispatchQueue.main.async { [weak self] in
            self?.updateSessionState(.error(CameraError.unknown(error)))
            self?.dataOutputDelegate?.cameraDidEncounterError(error)
        }
    }

    /// 会话开始运行通知
    @objc private func sessionDidStartRunning(notification: NSNotification) {
        DebugLogger.info("相机会话开始运行")
        DispatchQueue.main.async { [weak self] in
            self?.updateSessionState(.running)
        }
    }

    /// 会话停止运行通知
    @objc private func sessionDidStopRunning(notification: NSNotification) {
        DebugLogger.info("相机会话停止运行")
        DispatchQueue.main.async { [weak self] in
            self?.updateSessionState(.stopped)
        }
    }

    /// 应用进入后台
    @objc private func applicationWillResignActive(notification: NSNotification) {
        DebugLogger.info("应用进入后台，停止相机会话")
        stopSession()
    }

    /// 应用回到前台
    @objc private func applicationDidBecomeActive(notification: NSNotification) {
        DebugLogger.info("应用回到前台")
        if case .stopped = sessionState {
            startSession()
                .sink { _ in }
                .store(in: &cancellables)
        }
    }
}

// MARK: - 弱引用辅助类

/// 弱引用包装器，用于避免循环引用
class WeakReference<T: AnyObject> {
    weak var value: T?

    init(_ value: T) {
        self.value = value
    }
}
