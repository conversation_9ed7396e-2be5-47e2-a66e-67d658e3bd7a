import Foundation
import AVFoundation
import Combine
import MediaPipeTasksVision
import UIKit

// MARK: - 人体姿态检测服务实现类

/// 人体姿态检测服务实现
/// 负责管理MediaPipe姿态检测、坐标转换、结果处理等核心功能
final class PoseDetectionService: NSObject, PoseDetectionServiceProtocol {

    // MARK: - 发布属性

    /// 检测配置参数
    @Published var configuration: PoseDetectionConfiguration = .default

    /// 最新的检测结果
    @Published private(set) var latestResult: PoseDetectionResult?

    /// 检测状态
    @Published private(set) var isDetecting: Bool = false

    /// 错误信息
    @Published private(set) var errorMessage: String?

    // MARK: - 私有属性

    /// MediaPipe姿态检测器
    private var poseLandmarker: PoseLandmarker?

    /// 检测队列，用于异步处理检测任务
    private let detectionQueue = DispatchQueue(label: "com.fitcount.pose.detection", qos: .userInitiated)

    /// Combine取消令牌集合
    private var cancellables = Set<AnyCancellable>()

    /// 结果代理
    weak var resultDelegate: PoseDetectionResultDelegate?

    /// 坐标转换器
    private let coordinateTransformer: PoseCoordinateTransformerProtocol

    // MARK: - 初始化

    /// 初始化姿态检测服务
    /// - Parameter coordinateTransformer: 坐标转换器，如果为nil则使用默认实现
    init(coordinateTransformer: PoseCoordinateTransformerProtocol? = nil) {
        self.coordinateTransformer = coordinateTransformer ?? DefaultPoseCoordinateTransformer()
        super.init()
        DebugLogger.info("PoseDetectionService初始化")
    }

    deinit {
        DebugLogger.info("PoseDetectionService销毁")
        stopLiveDetection()
    }

    // MARK: - PoseDetectionServiceProtocol实现

    /// 初始化姿态检测服务
    /// - Parameter configuration: 检测配置参数
    /// - Returns: 初始化结果的发布者
    func initialize(with configuration: PoseDetectionConfiguration) -> AnyPublisher<Bool, Error> {
        DebugLogger.info("初始化姿态检测服务")

        self.configuration = configuration

        return Future<Bool, Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(PoseDetectionError.serviceReleased))
                return
            }

            self.detectionQueue.async {
                do {
                    self.poseLandmarker = try self.createPoseLandmarker()

                    DispatchQueue.main.async {
                        self.errorMessage = nil
                        promise(.success(true))
                    }
                } catch {
                    DispatchQueue.main.async {
                        self.errorMessage = error.localizedDescription
                        promise(.failure(error))
                    }
                }
            }
        }
        .eraseToAnyPublisher()
    }

    /// 开始实时检测
    /// - Returns: 启动结果的发布者
    func startLiveDetection() -> AnyPublisher<Bool, Error> {
        DebugLogger.info("开始实时姿态检测")

        return Future<Bool, Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(PoseDetectionError.serviceReleased))
                return
            }

            guard self.poseLandmarker != nil else {
                promise(.failure(PoseDetectionError.notInitialized))
                return
            }

            DispatchQueue.main.async {
                self.isDetecting = true
                self.resultDelegate?.poseDetectionStateDidChange(isDetecting: true)
                promise(.success(true))
            }
        }
        .eraseToAnyPublisher()
    }

    /// 停止实时检测
    func stopLiveDetection() {
        DebugLogger.info("停止实时姿态检测")

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isDetecting = false
            self.resultDelegate?.poseDetectionStateDidChange(isDetecting: false)
        }
    }

    /// 检测单张图像中的人体姿态
    /// - Parameter image: 输入图像
    /// - Returns: 检测结果的发布者
    func detectPose(in image: UIImage) -> AnyPublisher<PoseDetectionResult?, Error> {
        DebugLogger.debug("检测单张图像中的人体姿态")

        return Future<PoseDetectionResult?, Error> { [weak self] promise in
            guard let self = self else {
                promise(.failure(PoseDetectionError.serviceReleased))
                return
            }

            guard let poseLandmarker = self.poseLandmarker else {
                promise(.failure(PoseDetectionError.notInitialized))
                return
            }

            self.detectionQueue.async {
                do {
                    guard let mpImage = try? MPImage(uiImage: image) else {
                        promise(.failure(PoseDetectionError.imageConversionFailed))
                        return
                    }

                    let result = try poseLandmarker.detect(image: mpImage)

                    let detectionResult = self.processDetectionResult(
                        result,
                        imageSize: image.size,
                        timestamp: Date().timeIntervalSince1970
                    )

                    DispatchQueue.main.async {
                        promise(.success(detectionResult))
                    }
                } catch {
                    DispatchQueue.main.async {
                        promise(.failure(error))
                    }
                }
            }
        }
        .eraseToAnyPublisher()
    }

    /// 异步检测视频帧中的人体姿态
    /// - Parameters:
    ///   - sampleBuffer: 视频帧缓冲区
    ///   - orientation: 图像方向
    ///   - timestamp: 时间戳
    func detectPoseAsync(sampleBuffer: CMSampleBuffer, orientation: UIImage.Orientation, timestamp: Int) {
        guard isDetecting, let poseLandmarker = poseLandmarker else { return }

        detectionQueue.async { [weak self] in
            guard let self = self else { return }

            do {
                let mpImage = try MPImage(sampleBuffer: sampleBuffer, orientation: orientation)

                // 异步检测，结果会通过delegate回调
                try poseLandmarker.detectAsync(image: mpImage, timestampInMilliseconds: timestamp)
            } catch {
                DispatchQueue.main.async {
                    self.errorMessage = error.localizedDescription
                    self.resultDelegate?.poseDetectionDidComplete(result: nil, error: error)
                }
            }
        }
    }

    /// 重置检测器
    func reset() {
        DebugLogger.info("重置姿态检测器")

        stopLiveDetection()

        detectionQueue.async { [weak self] in
            self?.poseLandmarker = nil

            DispatchQueue.main.async {
                self?.latestResult = nil
                self?.errorMessage = nil
            }
        }
    }

    // MARK: - 私有方法

    /// 创建MediaPipe姿态检测器
    /// - Returns: 配置好的姿态检测器
    /// - Throws: 创建过程中的错误
    private func createPoseLandmarker() throws -> PoseLandmarker {
        DebugLogger.debug("创建MediaPipe姿态检测器")

        let options = PoseLandmarkerOptions()
        options.runningMode = configuration.enableLiveStream ? .liveStream : .image
        options.numPoses = configuration.maxPoses
        options.minPoseDetectionConfidence = configuration.minPoseDetectionConfidence
        options.minPosePresenceConfidence = configuration.minPosePresenceConfidence
        options.minTrackingConfidence = configuration.minTrackingConfidence

        // 设置模型路径
        guard let modelPath = InferenceConfigurationManager.sharedInstance.model.modelPath else {
            throw PoseDetectionError.modelNotFound
        }
        options.baseOptions.modelAssetPath = modelPath
        options.baseOptions.delegate = InferenceConfigurationManager.sharedInstance.delegate.delegate

        // 如果是实时检测模式，设置代理
        if configuration.enableLiveStream {
            options.poseLandmarkerLiveStreamDelegate = self
        }

        return try PoseLandmarker(options: options)
    }

    /// 处理检测结果
    /// - Parameters:
    ///   - result: MediaPipe检测结果
    ///   - imageSize: 图像尺寸
    ///   - timestamp: 时间戳
    /// - Returns: 处理后的检测结果
    private func processDetectionResult(_ result: PoseLandmarkerResult?, imageSize: CGSize, timestamp: TimeInterval) -> PoseDetectionResult? {
        guard let result = result,
              !result.landmarks.isEmpty,
              let firstPersonLandmarks = result.landmarks.first else {
            return nil
        }

        // 计算平均置信度（这里简化处理，实际可能需要更复杂的置信度计算）
        let confidence: Float = 0.8 // 简化处理

        return PoseDetectionResult(
            landmarks: firstPersonLandmarks,
            confidence: confidence,
            timestamp: timestamp,
            imageSize: imageSize
        )
    }
}

// MARK: - PoseLandmarkerLiveStreamDelegate

extension PoseDetectionService: PoseLandmarkerLiveStreamDelegate {

    /// MediaPipe实时检测结果回调
    func poseLandmarker(_ poseLandmarker: PoseLandmarker, didFinishDetection result: PoseLandmarkerResult?, timestampInMilliseconds: Int, error: Error?) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            if let error = error {
                self.errorMessage = error.localizedDescription
                self.resultDelegate?.poseDetectionDidComplete(result: nil, error: error)
                return
            }

            // 处理检测结果
            let detectionResult = self.processDetectionResult(
                result,
                imageSize: CGSize(width: 640, height: 480), // 默认尺寸，实际应该从相机获取
                timestamp: Double(timestampInMilliseconds) / 1000.0
            )

            if let detectionResult = detectionResult {
                self.latestResult = detectionResult
                self.resultDelegate?.poseDetectionDidUpdate(result: detectionResult)
            }

            self.resultDelegate?.poseDetectionDidComplete(result: detectionResult, error: nil)
        }
    }
}

// MARK: - 姿态检测错误定义

/// 姿态检测错误类型
enum PoseDetectionError: Error, LocalizedError {
    /// 服务实例已释放
    case serviceReleased
    /// 检测器未初始化
    case notInitialized
    /// 模型文件未找到
    case modelNotFound
    /// 图像转换失败
    case imageConversionFailed
    /// 检测失败
    case detectionFailed(Error)

    /// 错误描述
    var errorDescription: String? {
        switch self {
        case .serviceReleased:
            return "姿态检测服务实例已释放"
        case .notInitialized:
            return "姿态检测器未初始化"
        case .modelNotFound:
            return "姿态检测模型文件未找到"
        case .imageConversionFailed:
            return "图像格式转换失败"
        case .detectionFailed(let error):
            return "姿态检测失败: \(error.localizedDescription)"
        }
    }
}

// MARK: - 默认坐标转换器实现

/// 默认的姿态坐标转换器实现
/// 负责将MediaPipe的归一化坐标转换为视图坐标系
class DefaultPoseCoordinateTransformer: PoseCoordinateTransformerProtocol {

    /// 将归一化坐标转换为视图坐标
    /// - Parameters:
    ///   - landmarks: 归一化关键点数组
    ///   - imageSize: 原始图像尺寸
    ///   - viewSize: 目标视图尺寸
    ///   - contentMode: 内容模式
    ///   - orientation: 图像方向
    /// - Returns: 转换后的视图坐标数组
    func transformLandmarks(_ landmarks: [NormalizedLandmark],
                          from imageSize: CGSize,
                          to viewSize: CGSize,
                          contentMode: UIView.ContentMode,
                          orientation: UIImage.Orientation) -> [CGPoint] {

        guard !landmarks.isEmpty else { return [] }

        // 计算缩放和偏移参数
        let (xOffset, yOffset, scaleFactor) = calculateOffsetsAndScaleFactor(
            imageSize: imageSize,
            viewSize: viewSize,
            contentMode: contentMode
        )

        // 根据设备方向转换坐标
        let transformedLandmarks = transformCoordinatesForOrientation(landmarks, orientation: orientation)

        // 将归一化坐标转换为视图坐标
        return transformedLandmarks.map { landmark in
            CGPoint(
                x: CGFloat(landmark.x) * imageSize.width * scaleFactor + xOffset,
                y: CGFloat(landmark.y) * imageSize.height * scaleFactor + yOffset
            )
        }
    }

    /// 创建姿态叠加数据
    /// - Parameters:
    ///   - landmarks: 归一化关键点数组
    ///   - imageSize: 原始图像尺寸
    ///   - viewSize: 目标视图尺寸
    ///   - contentMode: 内容模式
    ///   - orientation: 图像方向
    /// - Returns: 姿态叠加数据
    func createOverlayData(from landmarks: [NormalizedLandmark],
                          imageSize: CGSize,
                          viewSize: CGSize,
                          contentMode: UIView.ContentMode,
                          orientation: UIImage.Orientation) -> PoseVisualizationData? {

        guard !landmarks.isEmpty else { return nil }

        // 转换关键点坐标
        let dots = transformLandmarks(landmarks, from: imageSize, to: viewSize, contentMode: contentMode, orientation: orientation)

        // 创建连接线
        let lines = createPoseConnections(from: dots)

        return PoseVisualizationData(dots: dots, lines: lines)
    }

    // MARK: - 私有辅助方法

    /// 计算缩放和偏移参数
    /// - Parameters:
    ///   - imageSize: 图像尺寸
    ///   - viewSize: 视图尺寸
    ///   - contentMode: 内容模式
    /// - Returns: (x偏移, y偏移, 缩放因子)
    private func calculateOffsetsAndScaleFactor(imageSize: CGSize, viewSize: CGSize, contentMode: UIView.ContentMode) -> (CGFloat, CGFloat, CGFloat) {

        let widthScale = viewSize.width / imageSize.width
        let heightScale = viewSize.height / imageSize.height

        let scaleFactor: CGFloat
        switch contentMode {
        case .scaleAspectFill:
            scaleFactor = max(widthScale, heightScale)
        case .scaleAspectFit:
            scaleFactor = min(widthScale, heightScale)
        default:
            scaleFactor = 1.0
        }

        let scaledSize = CGSize(
            width: imageSize.width * scaleFactor,
            height: imageSize.height * scaleFactor
        )

        let xOffset = (viewSize.width - scaledSize.width) / 2
        let yOffset = (viewSize.height - scaledSize.height) / 2

        return (xOffset, yOffset, scaleFactor)
    }

    /// 根据设备方向转换坐标
    /// - Parameters:
    ///   - landmarks: 原始关键点
    ///   - orientation: 图像方向
    /// - Returns: 转换后的关键点
    private func transformCoordinatesForOrientation(_ landmarks: [NormalizedLandmark], orientation: UIImage.Orientation) -> [CGPoint] {

        // MediaPipe返回的坐标系统：
        // - (0,0) 在左上角
        // - (1,1) 在右下角
        // 需要根据设备方向进行坐标变换

        switch orientation {
        case .left:
            // 向左旋转90度
            return landmarks.map { CGPoint(x: CGFloat($0.y), y: 1 - CGFloat($0.x)) }
        case .right:
            // 向右旋转90度
            return landmarks.map { CGPoint(x: 1 - CGFloat($0.y), y: CGFloat($0.x)) }
        case .up:
            // 竖屏模式，相机通常是横向的，需要旋转
            return landmarks.map { CGPoint(x: 1 - CGFloat($0.y), y: CGFloat($0.x)) }
        default:
            // 默认情况，使用相同的变换
            return landmarks.map { CGPoint(x: 1 - CGFloat($0.y), y: CGFloat($0.x)) }
        }
    }

    /// 创建人体姿态连接线
    /// - Parameter dots: 关键点坐标数组
    /// - Returns: 连接线数组
    private func createPoseConnections(from dots: [CGPoint]) -> [PoseLine] {
        // 使用MediaPipe的姿态连接定义
        return PoseLandmarker.poseLandmarks.compactMap { connection in
            let startIndex = Int(connection.start)
            let endIndex = Int(connection.end)

            guard startIndex < dots.count && endIndex < dots.count else {
                return nil
            }

            return PoseConnectionLine(from: dots[startIndex], to: dots[endIndex])
        }
    }
}
