import SwiftUI

// MARK: - 状态指示器视图组件

/// 状态指示器视图
/// 用于显示各种状态信息和操作按钮
struct StatusIndicatorView: View {
    
    // MARK: - 状态类型枚举
    
    /// 状态指示器类型
    enum IndicatorType {
        case info       // 信息
        case warning    // 警告
        case error      // 错误
        case success    // 成功
        case loading    // 加载中
        
        /// 获取对应的颜色
        var color: Color {
            switch self {
            case .info:
                return .blue
            case .warning:
                return .orange
            case .error:
                return .red
            case .success:
                return .green
            case .loading:
                return .gray
            }
        }
        
        /// 获取对应的图标
        var icon: String {
            switch self {
            case .info:
                return "info.circle"
            case .warning:
                return "exclamationmark.triangle"
            case .error:
                return "xmark.circle"
            case .success:
                return "checkmark.circle"
            case .loading:
                return "clock"
            }
        }
    }
    
    // MARK: - 属性
    
    /// 显示的消息
    let message: String
    
    /// 指示器类型
    let type: IndicatorType
    
    /// 是否显示重试按钮
    let showRetryButton: Bool
    
    /// 重试按钮点击回调
    let onRetry: (() -> Void)?
    
    /// 是否显示动画
    @State private var isAnimating = false
    
    // MARK: - 初始化
    
    /// 初始化状态指示器
    /// - Parameters:
    ///   - message: 显示的消息
    ///   - type: 指示器类型，默认为info
    ///   - showRetryButton: 是否显示重试按钮，默认为false
    ///   - onRetry: 重试按钮点击回调
    init(message: String,
         type: IndicatorType = .info,
         showRetryButton: Bool = false,
         onRetry: (() -> Void)? = nil) {
        self.message = message
        self.type = type
        self.showRetryButton = showRetryButton
        self.onRetry = onRetry
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        VStack(spacing: 16) {
            // 图标和消息
            HStack(spacing: 12) {
                // 状态图标
                Image(systemName: type.icon)
                    .font(.title2)
                    .foregroundColor(type.color)
                    .scaleEffect(isAnimating && type == .loading ? 1.2 : 1.0)
                    .animation(
                        type == .loading ? 
                        Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true) : 
                        .default,
                        value: isAnimating
                    )
                
                // 消息文本
                Text(message)
                    .font(.body)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)
                    .fixedSize(horizontal: false, vertical: true)
                
                Spacer()
            }
            
            // 重试按钮
            if showRetryButton {
                Button(action: {
                    onRetry?()
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text(LocalizationStrings.Common.retry)
                    }
                    .font(.body)
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(type.color)
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.7))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(type.color.opacity(0.5), lineWidth: 1)
                )
        )
        .onAppear {
            if type == .loading {
                isAnimating = true
            }
        }
        .onDisappear {
            isAnimating = false
        }
    }
}

// MARK: - 调试信息视图

/// 调试信息视图
/// 用于在开发阶段显示详细的状态信息
struct DebugInfoView: View {
    
    /// 相机状态
    let cameraState: CameraSessionState
    
    /// 姿态检测状态
    let poseDetectionState: Bool
    
    /// 最后的检测结果
    let lastDetectionResult: PoseDetectionResult?
    
    /// 是否展开详细信息
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 标题栏
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                
                Text("调试信息")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                }) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.blue)
                }
            }
            
            if isExpanded {
                VStack(alignment: .leading, spacing: 4) {
                    // 相机状态
                    debugInfoRow(title: "相机状态", value: cameraState.description)
                    
                    // 姿态检测状态
                    debugInfoRow(title: "姿态检测", value: poseDetectionState ? "运行中" : "已停止")
                    
                    // 检测结果信息
                    if let result = lastDetectionResult {
                        debugInfoRow(title: "检测置信度", value: String(format: "%.2f", result.confidence))
                        debugInfoRow(title: "关键点数量", value: "\(result.landmarks.count)")
                        debugInfoRow(title: "检测时间", value: formatTimestamp(result.timestamp))
                    } else {
                        debugInfoRow(title: "检测结果", value: "无")
                    }
                    
                    // 系统信息
                    debugInfoRow(title: "设备方向", value: UIDevice.current.orientation.description)
                    debugInfoRow(title: "内存使用", value: getMemoryUsage())
                }
                .transition(.opacity.combined(with: .slide))
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    /// 调试信息行
    /// - Parameters:
    ///   - title: 标题
    ///   - value: 值
    /// - Returns: 调试信息行视图
    private func debugInfoRow(title: String, value: String) -> some View {
        HStack {
            Text(title + ":")
                .font(.caption)
                .foregroundColor(.gray)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .foregroundColor(.white)
                .lineLimit(1)
        }
    }
    
    /// 格式化时间戳
    /// - Parameter timestamp: 时间戳
    /// - Returns: 格式化后的时间字符串
    private func formatTimestamp(_ timestamp: TimeInterval) -> String {
        let date = Date(timeIntervalSince1970: timestamp)
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter.string(from: date)
    }
    
    /// 获取内存使用情况
    /// - Returns: 内存使用字符串
    private func getMemoryUsage() -> String {
        let info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let usedMB = Double(info.resident_size) / 1024.0 / 1024.0
            return String(format: "%.1f MB", usedMB)
        } else {
            return "未知"
        }
    }
}

// MARK: - UIDeviceOrientation扩展

extension UIDeviceOrientation {
    
    /// 获取方向描述
    var description: String {
        switch self {
        case .portrait:
            return "竖屏"
        case .portraitUpsideDown:
            return "倒置竖屏"
        case .landscapeLeft:
            return "左横屏"
        case .landscapeRight:
            return "右横屏"
        case .faceUp:
            return "面朝上"
        case .faceDown:
            return "面朝下"
        case .unknown:
            return "未知"
        @unknown default:
            return "未知"
        }
    }
}

// MARK: - 预览

struct StatusIndicatorView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            StatusIndicatorView(
                message: "相机正在初始化...",
                type: .loading
            )
            
            StatusIndicatorView(
                message: "相机权限被拒绝，请在设置中允许访问相机",
                type: .error,
                showRetryButton: true,
                onRetry: {
                    print("重试按钮被点击")
                }
            )
            
            StatusIndicatorView(
                message: "姿态检测已启用",
                type: .success
            )
        }
        .padding()
        .background(Color.blue)
        .previewLayout(.sizeThatFits)
    }
}
