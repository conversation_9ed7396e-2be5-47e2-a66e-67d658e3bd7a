import Foundation

// MARK: - 本地化字符串管理

/// 本地化字符串管理器
/// 统一管理应用中的所有本地化字符串，便于维护和国际化
struct LocalizationStrings {
    
    // MARK: - 通用字符串
    
    /// 通用操作字符串
    struct Common {
        static let ok = "确定"
        static let cancel = "取消"
        static let done = "完成"
        static let back = "返回"
        static let next = "下一步"
        static let previous = "上一步"
        static let save = "保存"
        static let delete = "删除"
        static let edit = "编辑"
        static let add = "添加"
        static let remove = "移除"
        static let close = "关闭"
        static let open = "打开"
        static let retry = "重试"
        static let refresh = "刷新"
        static let loading = "加载中..."
        static let error = "错误"
        static let warning = "警告"
        static let info = "信息"
        static let success = "成功"
        static let failed = "失败"
        static let unknown = "未知"
        static let none = "无"
        static let all = "全部"
        static let reset = "重置"
        static let settings = "设置"
        static let about = "关于"
        static let version = "版本"
        static let enable = "启用"
        static let disable = "禁用"
        static let on = "开"
        static let off = "关"
        static let yes = "是"
        static let no = "否"
    }
    
    // MARK: - 相机相关字符串
    
    /// 相机功能相关字符串
    struct Camera {
        static let title = "相机"
        static let toggle = "切换摄像头"
        static let frontCamera = "前置摄像头"
        static let backCamera = "后置摄像头"
        static let initializing = "摄像头初始化中..."
        static let interrupted = "相机访问已中断"
        static let error = "相机错误"
        static let permissionDenied = "相机权限被拒绝"
        static let permissionRestricted = "相机权限受限制"
        static let deviceNotAvailable = "相机设备不可用"
        static let configurationFailed = "相机配置失败"
        static let sessionStartFailed = "相机会话启动失败"
        static let restartCamera = "重新启动相机"
        static let checkStatus = "检查相机状态"
        static let permissionMessage = "请在设置中允许访问相机"
        static let sessionRunning = "相机正在运行"
        static let sessionStopped = "相机已停止"
        static let sessionConfiguring = "正在配置相机..."
        static let sessionConfigured = "相机配置完成"
        static let switchingCamera = "正在切换摄像头..."
        static let cameraReady = "相机准备就绪"
    }
    
    // MARK: - 姿态检测相关字符串
    
    /// 姿态检测功能相关字符串
    struct PoseDetection {
        static let title = "姿态检测"
        static let enable = "启用姿态检测"
        static let disable = "禁用姿态检测"
        static let detecting = "正在检测..."
        static let detected = "检测完成"
        static let noDetection = "未检测到人体"
        static let multiplePersons = "检测到多人"
        static let lowConfidence = "检测置信度较低"
        static let initializationFailed = "姿态检测初始化失败"
        static let modelNotFound = "姿态检测模型未找到"
        static let detectionError = "姿态检测错误"
        static let showKeypoints = "显示关键点"
        static let showConnections = "显示连接线"
        static let hideOverlay = "隐藏叠加层"
        static let showOverlay = "显示叠加层"
        static let highContrast = "高对比度"
        static let normalContrast = "普通对比度"
        static let minimalMode = "简约模式"
        static let fullMode = "完整模式"
    }
    
    // MARK: - 设置相关字符串
    
    /// 设置界面相关字符串
    struct Settings {
        static let title = "设置选项"
        static let general = "通用设置"
        static let display = "显示设置"
        static let notification = "通知设置"
        static let camera = "相机设置"
        static let poseDetection = "动作识别"
        static let about = "关于"
        
        // 通用设置
        static let enableSound = "启用声音"
        static let enableVibration = "启用振动"
        static let autoSave = "自动保存"
        static let showCounter = "显示计数器"
        
        // 显示设置
        static let showGrid = "显示网格线"
        static let disturbanceMode = "无干扰模式"
        static let brightness = "亮度"
        static let contrast = "对比度"
        
        // 通知设置
        static let soundAlert = "声音提示"
        static let vibrationFeedback = "振动反馈"
        static let pushNotification = "推送通知"
        
        // 相机设置
        static let autoFocus = "自动对焦"
        static let flashlight = "闪光灯"
        static let resolution = "分辨率"
        static let frameRate = "帧率"
        
        // 动作识别设置
        static let enableRecognition = "启用动作识别"
        static let sensitivity = "灵敏度"
        static let confidence = "置信度"
        static let maxPersons = "最大检测人数"
        
        // 其他
        static let resetAllSettings = "重置所有设置"
        static let exportSettings = "导出设置"
        static let importSettings = "导入设置"
        static let restoreDefaults = "恢复默认设置"
    }
    
    // MARK: - 运动相关字符串
    
    /// 运动和锻炼相关字符串
    struct Exercise {
        static let sitUps = "仰卧起坐"
        static let pushUps = "俯卧撑"
        static let squats = "深蹲"
        static let plank = "平板支撑"
        static let jumpingJacks = "开合跳"
        static let burpees = "波比跳"
        static let lunges = "弓步蹲"
        static let mountainClimbers = "登山者"
        
        static let startExercise = "开始锻炼"
        static let stopExercise = "停止锻炼"
        static let pauseExercise = "暂停锻炼"
        static let resumeExercise = "继续锻炼"
        static let completeExercise = "完成锻炼"
        
        static let count = "计数"
        static let duration = "持续时间"
        static let calories = "卡路里"
        static let sets = "组数"
        static let reps = "次数"
        static let rest = "休息"
        
        static let goodForm = "动作标准"
        static let improveForm = "动作需要改进"
        static let tooFast = "动作过快"
        static let tooSlow = "动作过慢"
    }
    
    // MARK: - 错误消息字符串
    
    /// 错误消息相关字符串
    struct ErrorMessages {
        static let networkError = "网络连接错误"
        static let serverError = "服务器错误"
        static let dataError = "数据错误"
        static let fileError = "文件错误"
        static let permissionError = "权限错误"
        static let configurationError = "配置错误"
        static let initializationError = "初始化错误"
        static let processingError = "处理错误"
        static let unknownError = "未知错误"
        
        static let tryAgain = "请重试"
        static let contactSupport = "请联系技术支持"
        static let checkSettings = "请检查设置"
        static let checkPermissions = "请检查权限"
        static let checkNetwork = "请检查网络连接"
        static let restartApp = "请重启应用"
        static let updateApp = "请更新应用"
    }
    
    // MARK: - 状态消息字符串
    
    /// 状态消息相关字符串
    struct StatusMessages {
        static let ready = "准备就绪"
        static let starting = "正在启动..."
        static let stopping = "正在停止..."
        static let processing = "正在处理..."
        static let saving = "正在保存..."
        static let loading = "正在加载..."
        static let connecting = "正在连接..."
        static let disconnecting = "正在断开连接..."
        static let updating = "正在更新..."
        static let syncing = "正在同步..."
        static let completed = "已完成"
        static let cancelled = "已取消"
        static let paused = "已暂停"
        static let resumed = "已恢复"
        static let failed = "失败"
        static let timeout = "超时"
        static let interrupted = "已中断"
        static let unavailable = "不可用"
    }
    
    // MARK: - 辅助方法
    
    /// 获取本地化字符串
    /// - Parameters:
    ///   - key: 字符串键
    ///   - comment: 注释
    /// - Returns: 本地化后的字符串
    static func localized(_ key: String, comment: String = "") -> String {
        return NSLocalizedString(key, comment: comment)
    }
    
    /// 格式化字符串
    /// - Parameters:
    ///   - format: 格式字符串
    ///   - arguments: 参数
    /// - Returns: 格式化后的字符串
    static func formatted(_ format: String, _ arguments: CVarArg...) -> String {
        return String(format: format, arguments: arguments)
    }
}

// MARK: - String扩展

extension String {
    
    /// 本地化字符串
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    /// 带注释的本地化字符串
    /// - Parameter comment: 注释
    /// - Returns: 本地化后的字符串
    func localized(comment: String) -> String {
        return NSLocalizedString(self, comment: comment)
    }
}

// MARK: - 兼容性字符串（保持与原代码的兼容性）

extension String {
    // 相机相关
    static let cameraToggle = LocalizationStrings.Camera.toggle
    static let cameraError = LocalizationStrings.Camera.error
    static let okButton = LocalizationStrings.Common.ok
    static let backButton = LocalizationStrings.Common.back
    static let cameraInitializing = LocalizationStrings.Camera.initializing
    static let cameraInterrupted = LocalizationStrings.Camera.interrupted
    static let errorPrefix = LocalizationStrings.Common.error + ": "
    static let restartCamera = LocalizationStrings.Camera.restartCamera
    static let checkCameraStatus = LocalizationStrings.Camera.checkStatus
    
    // 设置相关
    static let settingsTitle = LocalizationStrings.Settings.title
    static let enableRecognition = LocalizationStrings.Settings.enableRecognition
    static let showCounter = LocalizationStrings.Settings.showCounter
    static let soundAlert = LocalizationStrings.Settings.soundAlert
}
