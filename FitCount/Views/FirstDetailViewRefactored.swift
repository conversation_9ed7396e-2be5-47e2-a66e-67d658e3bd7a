import SwiftUI
import AVFoundation
import Combine

// MARK: - 重构后的主视图

/// 重构后的运动详情主视图
/// 采用分层架构，将业务逻辑、UI层、数据层明确分离
/// 使用依赖注入和协议解耦等设计模式
struct FirstDetailViewRefactored: View {

    // MARK: - 环境和状态

    @Environment(\.dismiss) var dismiss
    @Environment(\.horizontalSizeClass) var horizontalSizeClass

    /// 运动名称
    let exerciseName: String

    // MARK: - 服务依赖（依赖注入）

    /// 相机服务
    @StateObject private var cameraService: CameraService

    /// 姿态检测服务
    @StateObject private var poseDetectionService: PoseDetectionService

    /// 姿态叠加视图管理器
    @StateObject private var overlayManager: PoseOverlayManager

    // MARK: - UI状态

    /// 是否显示设置面板
    @State private var showSettings = false

    /// 是否显示错误警告
    @State private var showErrorAlert = false

    /// 错误消息
    @State private var errorMessage = ""

    /// 是否显示调试信息
    @State private var showDebugInfo = false

    // MARK: - 初始化

    /// 初始化视图
    /// - Parameters:
    ///   - exerciseName: 运动名称
    ///   - cameraService: 相机服务，可选，用于依赖注入
    ///   - poseDetectionService: 姿态检测服务，可选，用于依赖注入
    init(exerciseName: String,
         cameraService: CameraService? = nil,
         poseDetectionService: PoseDetectionService? = nil) {
        self.exerciseName = exerciseName

        // 依赖注入：如果外部提供了服务实例，则使用外部实例，否则创建默认实例
        self._cameraService = StateObject(wrappedValue: cameraService ?? CameraService())
        self._poseDetectionService = StateObject(wrappedValue: poseDetectionService ?? PoseDetectionService())
        self._overlayManager = StateObject(wrappedValue: PoseOverlayManager())

        DebugLogger.info("FirstDetailView初始化，exerciseName=\(exerciseName)")
    }

    // MARK: - 视图主体

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景层：相机预览
                cameraPreviewLayer

                // 叠加层：人体姿态绘制
                poseOverlayLayer

                // 状态指示层
                statusIndicatorLayer

                // UI控制层
                controlsLayer
            }
            .ignoresSafeArea(.keyboard)
            .adaptToScreenOrientation(horizontalSizeClass: horizontalSizeClass)
        }
        .navigationTitle(exerciseName)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            toolbarContent
        }
        .sheet(isPresented: $showSettings) {
            settingsSheet
        }
        .alert(isPresented: $showErrorAlert) {
            errorAlert
        }
        .onAppear {
            setupView()
        }
        .onDisappear {
            cleanupView()
        }
        .onChange(of: cameraService.errorMessage) { errorMessage in
            handleCameraError(errorMessage)
        }
        .onChange(of: poseDetectionService.errorMessage) { errorMessage in
            handlePoseDetectionError(errorMessage)
        }
    }

    // MARK: - 视图组件

    /// 相机预览层
    private var cameraPreviewLayer: some View {
        Group {
            if let previewLayer = cameraService.previewLayer {
                CameraPreviewView(previewLayer: previewLayer)
                    .edgesIgnoringSafeArea(.all)
            } else {
                Color.black
                    .edgesIgnoringSafeArea(.all)
            }
        }
    }

    /// 姿态叠加层
    private var poseOverlayLayer: some View {
        Group {
            if let overlayView = overlayManager.overlayView {
                PoseOverlayView(poseOverlayView: overlayView)
                    .edgesIgnoringSafeArea(.all)
                    .allowsHitTesting(false)
            }
        }
    }

    /// 状态指示层
    private var statusIndicatorLayer: some View {
        VStack {
            Spacer()

            // 相机状态指示
            if !cameraService.sessionState.isRunning {
                StatusIndicatorView(
                    message: cameraService.sessionState.description,
                    type: cameraService.sessionState.hasError ? .error : .info,
                    showRetryButton: !cameraService.sessionState.isRunning,
                    onRetry: {
                        retryCamera()
                    }
                )
                .padding()
            }

            // 调试信息显示
            if showDebugInfo {
                DebugInfoView(
                    cameraState: cameraService.sessionState,
                    poseDetectionState: poseDetectionService.isDetecting,
                    lastDetectionResult: poseDetectionService.latestResult
                )
                .padding()
            }
        }
    }

    /// 控制层
    private var controlsLayer: some View {
        VStack(spacing: 0) {
            // 顶部控制栏
            topControlBar

            Spacer()

            // 底部控制栏（如果需要）
            // bottomControlBar
        }
    }

    /// 顶部控制栏
    private var topControlBar: some View {
        HStack {
            Text(exerciseName)
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .padding()

            Spacer()

            // 切换摄像头按钮
            Button(action: toggleCamera) {
                Image(systemName: "camera.rotate.fill")
                    .font(.title)
                    .foregroundColor(.white)
                    .padding()
            }
            .disabled(!cameraService.sessionState.isRunning)
        }
        .background(Color.black.opacity(0.3))
    }

    /// 工具栏内容
    private var toolbarContent: some ToolbarContent {
        Group {
            // 返回按钮
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: {
                    DebugLogger.debug("用户点击返回按钮")
                    dismiss()
                }) {
                    Text(LocalizationStrings.Common.back)
                }
            }

            // 设置按钮
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    showSettings = true
                }) {
                    Image(systemName: "slider.horizontal.3")
                }
            }

            // 调试按钮（仅在DEBUG模式下显示）
            #if DEBUG
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    showDebugInfo.toggle()
                }) {
                    Image(systemName: "info.circle")
                        .foregroundColor(showDebugInfo ? .blue : .primary)
                }
            }
            #endif
        }
    }

    /// 设置面板
    private var settingsSheet: some View {
        SettingsBottomSheet(
            isPresented: $showSettings,
            configuration: .default
        )
    }

    /// 错误警告
    private var errorAlert: Alert {
        Alert(
            title: Text(LocalizationStrings.Common.error),
            message: Text(errorMessage),
            dismissButton: .default(Text(LocalizationStrings.Common.ok))
        )
    }

    // MARK: - 业务逻辑方法

    /// 设置视图
    private func setupView() {
        DebugLogger.info("FirstDetailView显示，开始设置")

        // 设置服务间的依赖关系
        setupServiceDependencies()

        // 启动相机服务
        startCameraService()

        // 启动姿态检测服务
        startPoseDetectionService()
    }

    /// 清理视图
    private func cleanupView() {
        DebugLogger.info("FirstDetailView消失，开始清理")

        // 停止服务
        poseDetectionService.stopLiveDetection()
        cameraService.stopSession()

        // 清理叠加视图
        overlayManager.clearOverlay()
    }

    /// 设置服务间的依赖关系
    private func setupServiceDependencies() {
        // 设置相机数据输出代理
        cameraService.dataOutputDelegate = overlayManager

        // 设置姿态检测结果代理
        poseDetectionService.resultDelegate = overlayManager

        // 设置叠加管理器的坐标转换器
        overlayManager.coordinateTransformer = DefaultPoseCoordinateTransformer()
    }

    /// 启动相机服务
    private func startCameraService() {
        let configuration = CameraConfiguration.default

        cameraService.setupAndStartSession(with: configuration)
            .sink { status in
                DispatchQueue.main.async {
                    if !status.isSuccess {
                        self.handleCameraConfigurationError(status)
                    }
                }
            }
            .store(in: &cancellables)
    }

    /// 启动姿态检测服务
    private func startPoseDetectionService() {
        let configuration = PoseDetectionConfiguration.default

        poseDetectionService.initialize(with: configuration)
            .flatMap { _ in
                self.poseDetectionService.startLiveDetection()
            }
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self.handlePoseDetectionInitializationError(error)
                        }
                    }
                },
                receiveValue: { success in
                    DebugLogger.info("姿态检测服务启动\(success ? "成功" : "失败")")
                }
            )
            .store(in: &cancellables)
    }

    /// 切换摄像头
    private func toggleCamera() {
        DebugLogger.debug("用户点击切换摄像头")

        cameraService.toggleCamera()
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        DispatchQueue.main.async {
                            self.showError(error.localizedDescription)
                        }
                    }
                },
                receiveValue: { newPosition in
                    DebugLogger.info("摄像头切换到: \(newPosition.description)")
                }
            )
            .store(in: &cancellables)
    }

    /// 重试相机
    private func retryCamera() {
        DebugLogger.debug("用户点击重试相机")
        startCameraService()
    }

    // MARK: - 错误处理

    /// 处理相机错误
    private func handleCameraError(_ errorMessage: String?) {
        guard let errorMessage = errorMessage else { return }
        DebugLogger.error("相机错误: \(errorMessage)")
        showError(errorMessage)
    }

    /// 处理姿态检测错误
    private func handlePoseDetectionError(_ errorMessage: String?) {
        guard let errorMessage = errorMessage else { return }
        DebugLogger.error("姿态检测错误: \(errorMessage)")
        showError(errorMessage)
    }

    /// 处理相机配置错误
    private func handleCameraConfigurationError(_ status: CameraConfigurationStatus) {
        DebugLogger.error("相机配置错误: \(status.description)")
        showError(status.description)
    }

    /// 处理姿态检测初始化错误
    private func handlePoseDetectionInitializationError(_ error: Error) {
        DebugLogger.error("姿态检测初始化错误: \(error.localizedDescription)")
        showError(error.localizedDescription)
    }

    /// 显示错误
    private func showError(_ message: String) {
        errorMessage = message
        showErrorAlert = true
    }

    // MARK: - 私有属性

    /// Combine取消令牌集合
    @State private var cancellables = Set<AnyCancellable>()
}

// MARK: - 预览

struct FirstDetailViewRefactored_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            FirstDetailViewRefactored(exerciseName: "仰卧起坐")
        }
        .environment(\.locale, .init(identifier: "zh-Hans"))
    }
}
