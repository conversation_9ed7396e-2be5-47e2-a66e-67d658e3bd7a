import Foundation
import AVFoundation
import Combine
import MediaPipeTasksVision

// MARK: - 人体姿态检测协议定义

/// 人体姿态检测服务协议
/// 定义姿态检测的核心功能接口，实现业务逻辑与具体实现的解耦
protocol PoseDetectionServiceProtocol: ObservableObject {

    // MARK: - 发布属性

    /// 检测配置参数
    var configuration: PoseDetectionConfiguration { get set }

    /// 最新的检测结果
    var latestResult: PoseDetectionResult? { get }

    /// 检测状态
    var isDetecting: Bool { get }

    /// 错误信息
    var errorMessage: String? { get }

    // MARK: - 检测控制方法

    /// 初始化姿态检测服务
    /// - Parameter configuration: 检测配置参数
    /// - Returns: 初始化结果的发布者
    func initialize(with configuration: PoseDetectionConfiguration) -> AnyPublisher<Bool, Error>

    /// 开始实时检测
    /// - Returns: 启动结果的发布者
    func startLiveDetection() -> AnyPublisher<Bool, Error>

    /// 停止实时检测
    func stopLiveDetection()

    /// 检测单张图像中的人体姿态
    /// - Parameter image: 输入图像
    /// - Returns: 检测结果的发布者
    func detectPose(in image: UIImage) -> AnyPublisher<PoseDetectionResult?, Error>

    /// 异步检测视频帧中的人体姿态
    /// - Parameters:
    ///   - sampleBuffer: 视频帧缓冲区
    ///   - orientation: 图像方向
    ///   - timestamp: 时间戳
    func detectPoseAsync(sampleBuffer: CMSampleBuffer, orientation: UIImage.Orientation, timestamp: Int)

    /// 重置检测器
    func reset()
}

/// 人体姿态检测结果代理协议
/// 用于接收姿态检测的结果回调
protocol PoseDetectionResultDelegate: AnyObject {

    /// 检测完成回调
    /// - Parameters:
    ///   - result: 检测结果，可能为nil
    ///   - error: 检测过程中的错误，可能为nil
    func poseDetectionDidComplete(result: PoseDetectionResult?, error: Error?)

    /// 实时检测结果更新
    /// - Parameter result: 最新的检测结果
    func poseDetectionDidUpdate(result: PoseDetectionResult)

    /// 检测状态变化
    /// - Parameter isDetecting: 是否正在检测
    func poseDetectionStateDidChange(isDetecting: Bool)
}

/// 姿态叠加层渲染协议
/// 定义姿态可视化渲染的接口
protocol PoseOverlayRendererProtocol: AnyObject {

    /// 更新姿态叠加数据
    /// - Parameters:
    ///   - overlayData: 叠加数据
    ///   - imageSize: 原始图像尺寸
    ///   - viewSize: 视图尺寸
    ///   - contentMode: 内容模式
    func updateOverlay(overlayData: PoseVisualizationData?, imageSize: CGSize, viewSize: CGSize, contentMode: UIView.ContentMode)

    /// 清除叠加内容
    func clearOverlay()

    /// 设置渲染样式
    /// - Parameter style: 渲染样式配置
    func setRenderingStyle(_ style: PoseRenderingStyle)
}

/// 姿态渲染样式配置
/// 定义姿态可视化的样式参数
struct PoseRenderingStyle {
    /// 关键点颜色
    let pointColor: UIColor
    /// 关键点填充颜色
    let pointFillColor: UIColor
    /// 关键点半径
    let pointRadius: CGFloat
    /// 连接线颜色
    let lineColor: UIColor
    /// 连接线宽度
    let lineWidth: CGFloat
    /// 是否显示关键点
    let showPoints: Bool
    /// 是否显示连接线
    let showLines: Bool
    /// 透明度
    let alpha: CGFloat

    /// 初始化渲染样式
    /// - Parameters:
    ///   - pointColor: 关键点颜色，默认为黄色
    ///   - pointFillColor: 关键点填充颜色，默认为红色
    ///   - pointRadius: 关键点半径，默认为2.0
    ///   - lineColor: 连接线颜色，默认为青色
    ///   - lineWidth: 连接线宽度，默认为2.0
    ///   - showPoints: 是否显示关键点，默认为true
    ///   - showLines: 是否显示连接线，默认为true
    ///   - alpha: 透明度，默认为1.0
    init(pointColor: UIColor = .yellow,
         pointFillColor: UIColor = .red,
         pointRadius: CGFloat = 2.0,
         lineColor: UIColor = UIColor(red: 0, green: 127/255.0, blue: 139/255.0, alpha: 1),
         lineWidth: CGFloat = 2.0,
         showPoints: Bool = true,
         showLines: Bool = true,
         alpha: CGFloat = 1.0) {
        self.pointColor = pointColor
        self.pointFillColor = pointFillColor
        self.pointRadius = pointRadius
        self.lineColor = lineColor
        self.lineWidth = lineWidth
        self.showPoints = showPoints
        self.showLines = showLines
        self.alpha = alpha
    }

    /// 默认样式
    static var `default`: PoseRenderingStyle {
        return PoseRenderingStyle()
    }
}

/// 姿态坐标转换协议
/// 定义姿态坐标系转换的接口
protocol PoseCoordinateTransformerProtocol {

    /// 将归一化坐标转换为视图坐标
    /// - Parameters:
    ///   - landmarks: 归一化关键点数组
    ///   - imageSize: 原始图像尺寸
    ///   - viewSize: 目标视图尺寸
    ///   - contentMode: 内容模式
    ///   - orientation: 图像方向
    /// - Returns: 转换后的视图坐标数组
    func transformLandmarks(_ landmarks: [NormalizedLandmark],
                          from imageSize: CGSize,
                          to viewSize: CGSize,
                          contentMode: UIView.ContentMode,
                          orientation: UIImage.Orientation) -> [CGPoint]

    /// 创建姿态叠加数据
    /// - Parameters:
    ///   - landmarks: 归一化关键点数组
    ///   - imageSize: 原始图像尺寸
    ///   - viewSize: 目标视图尺寸
    ///   - contentMode: 内容模式
    ///   - orientation: 图像方向
    /// - Returns: 姿态叠加数据
    func createOverlayData(from landmarks: [NormalizedLandmark],
                          imageSize: CGSize,
                          viewSize: CGSize,
                          contentMode: UIView.ContentMode,
                          orientation: UIImage.Orientation) -> PoseVisualizationData?
}

// MARK: - 默认实现扩展

extension PoseDetectionServiceProtocol {

    /// 检查检测器是否已初始化
    /// - Returns: 是否已初始化
    func isInitialized() -> Bool {
        return latestResult != nil || isDetecting
    }

    /// 获取当前检测配置的描述
    /// - Returns: 配置描述字符串
    func getConfigurationDescription() -> String {
        return """
        最大检测人数: \(configuration.maxPoses)
        检测置信度: \(configuration.minPoseDetectionConfidence)
        存在置信度: \(configuration.minPosePresenceConfidence)
        跟踪置信度: \(configuration.minTrackingConfidence)
        实时检测: \(configuration.enableLiveStream ? "启用" : "禁用")
        """
    }
}
