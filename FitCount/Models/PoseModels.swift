import Foundation
import CoreGraphics
import MediaPipeTasksVision

// MARK: - 人体姿态检测相关数据模型

/// 人体姿态连接线数据结构
/// 表示两个关键点之间的连接线
struct PoseConnectionLine {
    /// 连接线起始点坐标
    let from: CGPoint
    /// 连接线结束点坐标
    let to: CGPoint

    /// 初始化连接线
    /// - Parameters:
    ///   - from: 起始点坐标
    ///   - to: 结束点坐标
    init(from: CGPoint, to: CGPoint) {
        self.from = from
        self.to = to
    }
}

/// 人体姿态叠加层显示数据
/// 包含用于在相机预览上绘制人体关键点和连接线的所有数据
struct PoseVisualizationData {
    /// 关键点坐标数组，用于绘制圆点
    let dots: [CGPoint]
    /// 连接线数组，用于绘制骨架连接
    let lines: [PoseConnectionLine]

    /// 初始化姿态叠加数据
    /// - Parameters:
    ///   - dots: 关键点坐标数组
    ///   - lines: 连接线数组
    init(dots: [CGPoint], lines: [PoseConnectionLine]) {
        self.dots = dots
        self.lines = lines
    }

    /// 创建空的姿态叠加数据
    static var empty: PoseVisualizationData {
        return PoseVisualizationData(dots: [], lines: [])
    }
}

/// 人体姿态检测结果数据模型
/// 封装MediaPipe检测结果，便于在应用中传递和处理
struct PoseDetectionResult {
    /// 检测到的人体关键点数组
    let landmarks: [NormalizedLandmark]
    /// 检测置信度
    let confidence: Float
    /// 检测时间戳
    let timestamp: TimeInterval
    /// 原始图像尺寸
    let imageSize: CGSize

    /// 初始化姿态检测结果
    /// - Parameters:
    ///   - landmarks: 检测到的关键点
    ///   - confidence: 检测置信度
    ///   - timestamp: 检测时间戳
    ///   - imageSize: 原始图像尺寸
    init(landmarks: [NormalizedLandmark], confidence: Float, timestamp: TimeInterval, imageSize: CGSize) {
        self.landmarks = landmarks
        self.confidence = confidence
        self.timestamp = timestamp
        self.imageSize = imageSize
    }

    /// 检查检测结果是否有效
    var isValid: Bool {
        return !landmarks.isEmpty && confidence > 0.5
    }
}

/// 人体姿态检测配置参数
/// 用于配置MediaPipe姿态检测的各种参数
struct PoseDetectionConfiguration {
    /// 最大检测人数
    let maxPoses: Int
    /// 最小姿态检测置信度
    let minPoseDetectionConfidence: Float
    /// 最小姿态存在置信度
    let minPosePresenceConfidence: Float
    /// 最小跟踪置信度
    let minTrackingConfidence: Float
    /// 是否启用实时检测
    let enableLiveStream: Bool

    /// 初始化姿态检测配置
    /// - Parameters:
    ///   - maxPoses: 最大检测人数，默认为1
    ///   - minPoseDetectionConfidence: 最小姿态检测置信度，默认为0.5
    ///   - minPosePresenceConfidence: 最小姿态存在置信度，默认为0.5
    ///   - minTrackingConfidence: 最小跟踪置信度，默认为0.5
    ///   - enableLiveStream: 是否启用实时检测，默认为true
    init(maxPoses: Int = 1,
         minPoseDetectionConfidence: Float = 0.5,
         minPosePresenceConfidence: Float = 0.5,
         minTrackingConfidence: Float = 0.5,
         enableLiveStream: Bool = true) {
        self.maxPoses = maxPoses
        self.minPoseDetectionConfidence = minPoseDetectionConfidence
        self.minPosePresenceConfidence = minPosePresenceConfidence
        self.minTrackingConfidence = minTrackingConfidence
        self.enableLiveStream = enableLiveStream
    }

    /// 默认配置
    static var `default`: PoseDetectionConfiguration {
        return PoseDetectionConfiguration()
    }
}
