import Foundation
import AVFoundation
import Combine

// MARK: - 相机服务协议定义

/// 相机服务协议
/// 定义相机管理的核心功能接口，实现业务逻辑与具体实现的解耦
protocol CameraServiceProtocol: ObservableObject {
    
    // MARK: - 发布属性
    
    /// 相机会话状态
    var sessionState: CameraSessionState { get }
    
    /// 当前使用的相机位置
    var currentPosition: CameraPosition { get }
    
    /// 相机预览层
    var previewLayer: AVCaptureVideoPreviewLayer? { get }
    
    /// 错误信息
    var errorMessage: String? { get }
    
    // MARK: - 相机控制方法
    
    /// 设置并启动相机会话
    /// - Parameter configuration: 相机配置参数
    /// - Returns: 配置结果的发布者
    func setupAndStartSession(with configuration: CameraConfiguration) -> AnyPublisher<CameraConfigurationStatus, Never>
    
    /// 启动相机会话
    /// - Returns: 启动结果的发布者
    func startSession() -> AnyPublisher<Bool, Never>
    
    /// 停止相机会话
    func stopSession()
    
    /// 切换前后摄像头
    /// - Returns: 切换结果的发布者
    func toggleCamera() -> AnyPublisher<CameraPosition, CameraError>
    
    /// 检查相机权限状态
    /// - Returns: 权限状态
    func checkCameraPermission() -> AVAuthorizationStatus
    
    /// 请求相机权限
    /// - Returns: 权限请求结果的发布者
    func requestCameraPermission() -> AnyPublisher<Bool, Never>
}

/// 相机数据输出代理协议
/// 用于处理相机输出的视频帧数据
protocol CameraDataOutputDelegate: AnyObject {
    
    /// 相机输出新的视频帧
    /// - Parameters:
    ///   - sampleBuffer: 视频帧缓冲区
    ///   - orientation: 设备方向
    func cameraDidOutput(sampleBuffer: CMSampleBuffer, orientation: UIImage.Orientation)
    
    /// 相机会话遇到运行时错误
    /// - Parameter error: 错误信息
    func cameraDidEncounterError(_ error: Error)
    
    /// 相机会话被中断
    /// - Parameter reason: 中断原因
    func cameraSessionWasInterrupted(reason: String)
    
    /// 相机会话中断结束
    func cameraSessionInterruptionEnded()
}

/// 相机状态观察者协议
/// 用于观察相机状态变化
protocol CameraStateObserver: AnyObject {
    
    /// 相机状态发生变化
    /// - Parameters:
    ///   - oldState: 旧状态
    ///   - newState: 新状态
    func cameraStateDidChange(from oldState: CameraSessionState, to newState: CameraSessionState)
    
    /// 相机位置发生变化
    /// - Parameters:
    ///   - oldPosition: 旧位置
    ///   - newPosition: 新位置
    func cameraPositionDidChange(from oldPosition: CameraPosition, to newPosition: CameraPosition)
}

/// 相机预览视图协议
/// 定义相机预览视图的基本功能
protocol CameraPreviewViewProtocol: AnyObject {
    
    /// 设置预览层
    /// - Parameter previewLayer: AVCaptureVideoPreviewLayer实例
    func setPreviewLayer(_ previewLayer: AVCaptureVideoPreviewLayer?)
    
    /// 更新预览层框架
    /// - Parameter frame: 新的框架尺寸
    func updatePreviewFrame(_ frame: CGRect)
    
    /// 清除预览内容
    func clearPreview()
}

// MARK: - 默认实现扩展

extension CameraServiceProtocol {
    
    /// 检查相机是否可用
    /// - Returns: 是否可用
    func isCameraAvailable() -> Bool {
        return AVCaptureDevice.default(for: .video) != nil
    }
    
    /// 获取可用的相机设备
    /// - Parameter position: 相机位置
    /// - Returns: 相机设备，如果不存在则返回nil
    func getCameraDevice(for position: CameraPosition) -> AVCaptureDevice? {
        return AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: position.avPosition)
    }
    
    /// 检查是否支持指定位置的相机
    /// - Parameter position: 相机位置
    /// - Returns: 是否支持
    func isCameraSupported(at position: CameraPosition) -> Bool {
        return getCameraDevice(for: position) != nil
    }
}
