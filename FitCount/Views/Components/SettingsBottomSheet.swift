import SwiftUI

// MARK: - 设置底部面板组件

/// 设置底部面板视图
/// 使用 sheet 和 presentationDetents 实现的可拖拽设置面板
struct SettingsBottomSheet: View {
    
    // MARK: - 面板停靠位置枚举
    
    /// 控制面板采用的停靠位置
    enum DetentHeight: CaseIterable {
        case small     // 小尺寸，约为屏幕高度的10%
        case medium    // 中等尺寸，约为屏幕高度的50%
        case large     // 大尺寸，约为屏幕高度的100%
        
        /// 获取对应的分数值
        var fraction: Double {
            switch self {
            case .small: return 0.1
            case .medium: return 0.5
            case .large: return 0.95
            }
        }
        
        /// 获取显示名称
        var displayName: String {
            switch self {
            case .small: return "小"
            case .medium: return "中"
            case .large: return "大"
            }
        }
    }
    
    // MARK: - 属性
    
    /// 控制面板关闭的状态绑定
    @Binding var isPresented: Bool
    
    /// 当前选中的停靠位置
    @State private var selectedDetent: DetentHeight = .medium
    
    /// 设置项配置
    let settingsConfiguration: SettingsConfiguration
    
    /// 初始化
    /// - Parameters:
    ///   - isPresented: 面板显示状态绑定
    ///   - configuration: 设置项配置
    init(isPresented: Binding<Bool>, configuration: SettingsConfiguration = .default) {
        self._isPresented = isPresented
        self.settingsConfiguration = configuration
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 0) {
                    // 拖动指示器
                    DragIndicator()
                        .padding(.vertical, 8)
                    
                    // 设置内容区域
                    settingsContent
                }
                .padding(.horizontal)
            }
            .navigationTitle("设置选项")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // 关闭按钮
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        isPresented = false
                    }) {
                        Text("完成")
                            .fontWeight(.bold)
                    }
                }
                
                // 面板大小切换按钮
                ToolbarItem(placement: .navigationBarLeading) {
                    Menu {
                        ForEach(DetentHeight.allCases, id: \.self) { detent in
                            Button(action: { selectedDetent = detent }) {
                                Label(detent.displayName, 
                                     systemImage: selectedDetent == detent ? "checkmark" : "")
                            }
                        }
                    } label: {
                        Image(systemName: "arrow.up.and.down")
                    }
                }
            }
            .background(Color(UIColor.systemGroupedBackground))
        }
        .presentationDetents([
            .fraction(DetentHeight.small.fraction),
            .fraction(DetentHeight.medium.fraction),
            .fraction(DetentHeight.large.fraction)
        ], selection: detentSelection)
        .presentationDragIndicator(.visible) // 显示系统拖动指示器
        .interactiveDismissDisabled() // 禁止交互式关闭（必须点击完成按钮）
    }
    
    // MARK: - 设置内容视图
    
    /// 设置面板主要内容
    private var settingsContent: some View {
        VStack(spacing: 20) {
            // 动作识别设置区域
            if settingsConfiguration.showPoseDetectionSettings {
                settingsSection(title: "动作识别") {
                    toggleItem("启用动作识别", isOn: true)
                    toggleItem("显示计数器", isOn: true)
                    toggleItem("显示关键点", isOn: true)
                }
            }
            
            // 通知设置区域
            if settingsConfiguration.showNotificationSettings {
                settingsSection(title: "通知设置") {
                    toggleItem("声音提示", isOn: false)
                    toggleItem("振动反馈", isOn: true)
                }
            }
            
            // 显示设置区域
            if settingsConfiguration.showDisplaySettings {
                settingsSection(title: "显示设置") {
                    toggleItem("显示网格线", isOn: false)
                    toggleItem("自动保存", isOn: true)
                    toggleItem("无干扰模式", isOn: false)
                    toggleItem("高对比度", isOn: false)
                }
            }
            
            // 相机设置区域
            if settingsConfiguration.showCameraSettings {
                settingsSection(title: "相机设置") {
                    toggleItem("自动对焦", isOn: true)
                    toggleItem("闪光灯", isOn: false)
                }
            }
            
            // 关于区域
            if settingsConfiguration.showAboutSection {
                settingsSection(title: "关于") {
                    HStack {
                        Text("版本")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.gray)
                    }
                    .padding(.vertical, 8)
                    
                    Button(action: {
                        // 重置所有设置
                    }) {
                        Text("重置所有设置")
                            .foregroundColor(.red)
                    }
                    .padding(.vertical, 8)
                }
            }
            
            // 底部空白，确保内容可滚动
            Spacer(minLength: 40)
        }
    }
    
    // MARK: - 辅助视图方法
    
    /// 创建设置区域
    /// - Parameters:
    ///   - title: 区域标题
    ///   - content: 区域内容
    /// - Returns: 设置区域视图
    private func settingsSection<Content: View>(title: String, @ViewBuilder content: () -> Content) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            Text(title)
                .font(.headline)
                .padding(.leading, 5)
            
            VStack(spacing: 0) {
                content()
            }
            .padding(.vertical, 5)
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(12)
        }
    }
    
    /// 创建开关项
    /// - Parameters:
    ///   - title: 开关标题
    ///   - isOn: 开关状态
    /// - Returns: 开关项视图
    private func toggleItem(_ title: String, isOn: Bool) -> some View {
        Toggle(LocalizedStringKey(title), isOn: .constant(isOn))
            .toggleStyle(SwitchToggleStyle(tint: .blue))
            .padding(.vertical, 8)
            .padding(.horizontal, 15)
    }
    
    /// 将选择的停靠高度转换为绑定值
    private var detentSelection: Binding<PresentationDetent> {
        Binding<PresentationDetent> {
            .fraction(selectedDetent.fraction)
        } set: { newValue in
            // 根据新值更新选中的停靠位置
            for detent in DetentHeight.allCases {
                if abs(newValue.description.contains("\(detent.fraction)") ? 0 : 1) == 0 {
                    selectedDetent = detent
                    break
                }
            }
        }
    }
}

// MARK: - 拖动指示器组件

/// 自定义拖动指示器
struct DragIndicator: View {
    var body: some View {
        Capsule()
            .fill(Color.gray.opacity(0.5))
            .frame(width: 40, height: 5)
    }
}

// MARK: - 设置配置

/// 设置面板配置
/// 用于控制设置面板中显示哪些设置项
struct SettingsConfiguration {
    /// 是否显示姿态检测设置
    let showPoseDetectionSettings: Bool
    /// 是否显示通知设置
    let showNotificationSettings: Bool
    /// 是否显示显示设置
    let showDisplaySettings: Bool
    /// 是否显示相机设置
    let showCameraSettings: Bool
    /// 是否显示关于区域
    let showAboutSection: Bool
    
    /// 初始化配置
    /// - Parameters:
    ///   - showPoseDetectionSettings: 是否显示姿态检测设置，默认为true
    ///   - showNotificationSettings: 是否显示通知设置，默认为true
    ///   - showDisplaySettings: 是否显示显示设置，默认为true
    ///   - showCameraSettings: 是否显示相机设置，默认为true
    ///   - showAboutSection: 是否显示关于区域，默认为true
    init(showPoseDetectionSettings: Bool = true,
         showNotificationSettings: Bool = true,
         showDisplaySettings: Bool = true,
         showCameraSettings: Bool = true,
         showAboutSection: Bool = true) {
        self.showPoseDetectionSettings = showPoseDetectionSettings
        self.showNotificationSettings = showNotificationSettings
        self.showDisplaySettings = showDisplaySettings
        self.showCameraSettings = showCameraSettings
        self.showAboutSection = showAboutSection
    }
    
    /// 默认配置
    static var `default`: SettingsConfiguration {
        return SettingsConfiguration()
    }
    
    /// 简化配置（只显示基本设置）
    static var minimal: SettingsConfiguration {
        return SettingsConfiguration(
            showPoseDetectionSettings: true,
            showNotificationSettings: false,
            showDisplaySettings: false,
            showCameraSettings: false,
            showAboutSection: false
        )
    }
}

// MARK: - 预览

struct SettingsBottomSheet_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            Text("主视图内容")
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.blue.opacity(0.3))
        }
        .sheet(isPresented: .constant(true)) {
            SettingsBottomSheet(isPresented: .constant(true))
        }
    }
}
