import Foundation
import AVFoundation
import CoreGraphics

// MARK: - 相机相关数据模型

/// 相机配置状态枚举
/// 表示相机配置的各种状态
enum CameraConfigurationStatus {
    /// 配置成功
    case success
    /// 相机权限被拒绝
    case permissionDenied
    /// 相机权限受限制
    case permissionRestricted
    /// 相机设备不可用
    case deviceNotAvailable
    /// 配置失败
    case configurationFailed(Error)
    
    /// 获取状态描述信息
    var description: String {
        switch self {
        case .success:
            return "相机配置成功"
        case .permissionDenied:
            return "相机权限被拒绝"
        case .permissionRestricted:
            return "相机权限受限制"
        case .deviceNotAvailable:
            return "相机设备不可用"
        case .configurationFailed(let error):
            return "相机配置失败: \(error.localizedDescription)"
        }
    }
    
    /// 检查配置是否成功
    var isSuccess: Bool {
        if case .success = self {
            return true
        }
        return false
    }
}

/// 相机设备位置枚举
/// 表示前置或后置摄像头
enum CameraPosition {
    /// 前置摄像头
    case front
    /// 后置摄像头
    case back
    
    /// 转换为AVCaptureDevice.Position
    var avPosition: AVCaptureDevice.Position {
        switch self {
        case .front:
            return .front
        case .back:
            return .back
        }
    }
    
    /// 获取相反位置
    var opposite: CameraPosition {
        switch self {
        case .front:
            return .back
        case .back:
            return .front
        }
    }
    
    /// 获取位置描述
    var description: String {
        switch self {
        case .front:
            return "前置摄像头"
        case .back:
            return "后置摄像头"
        }
    }
}

/// 相机会话状态枚举
/// 表示相机会话的运行状态
enum CameraSessionState {
    /// 未初始化
    case notInitialized
    /// 正在配置
    case configuring
    /// 配置完成，准备启动
    case configured
    /// 正在运行
    case running
    /// 已停止
    case stopped
    /// 被中断
    case interrupted(reason: String)
    /// 发生错误
    case error(Error)
    
    /// 获取状态描述
    var description: String {
        switch self {
        case .notInitialized:
            return "未初始化"
        case .configuring:
            return "正在配置相机..."
        case .configured:
            return "相机配置完成"
        case .running:
            return "相机正在运行"
        case .stopped:
            return "相机已停止"
        case .interrupted(let reason):
            return "相机被中断: \(reason)"
        case .error(let error):
            return "相机错误: \(error.localizedDescription)"
        }
    }
    
    /// 检查是否正在运行
    var isRunning: Bool {
        if case .running = self {
            return true
        }
        return false
    }
    
    /// 检查是否被中断
    var isInterrupted: Bool {
        if case .interrupted = self {
            return true
        }
        return false
    }
    
    /// 检查是否有错误
    var hasError: Bool {
        if case .error = self {
            return true
        }
        return false
    }
}

/// 相机配置参数
/// 用于配置相机会话的各种参数
struct CameraConfiguration {
    /// 相机位置
    let position: CameraPosition
    /// 会话预设质量
    let sessionPreset: AVCaptureSession.Preset
    /// 视频重力模式
    let videoGravity: AVLayerVideoGravity
    /// 是否自动丢弃延迟帧
    let alwaysDiscardsLateVideoFrames: Bool
    /// 视频输出格式
    let videoSettings: [String: Any]
    
    /// 初始化相机配置
    /// - Parameters:
    ///   - position: 相机位置，默认为后置
    ///   - sessionPreset: 会话预设，默认为高质量
    ///   - videoGravity: 视频重力模式，默认为填充
    ///   - alwaysDiscardsLateVideoFrames: 是否丢弃延迟帧，默认为true
    ///   - videoSettings: 视频输出格式设置
    init(position: CameraPosition = .back,
         sessionPreset: AVCaptureSession.Preset = .high,
         videoGravity: AVLayerVideoGravity = .resizeAspectFill,
         alwaysDiscardsLateVideoFrames: Bool = true,
         videoSettings: [String: Any]? = nil) {
        self.position = position
        self.sessionPreset = sessionPreset
        self.videoGravity = videoGravity
        self.alwaysDiscardsLateVideoFrames = alwaysDiscardsLateVideoFrames
        self.videoSettings = videoSettings ?? [String(kCVPixelBufferPixelFormatTypeKey): kCMPixelFormat_32BGRA]
    }
    
    /// 默认配置
    static var `default`: CameraConfiguration {
        return CameraConfiguration()
    }
}

/// 相机错误类型枚举
/// 定义相机操作中可能出现的各种错误
enum CameraError: Error, LocalizedError {
    /// 权限被拒绝
    case permissionDenied
    /// 设备不可用
    case deviceNotAvailable
    /// 输入创建失败
    case inputCreationFailed(Error)
    /// 输出配置失败
    case outputConfigurationFailed
    /// 会话配置失败
    case sessionConfigurationFailed
    /// 会话启动失败
    case sessionStartFailed
    /// 未知错误
    case unknown(Error)
    
    /// 错误描述
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "相机权限被拒绝，请在设置中允许访问相机"
        case .deviceNotAvailable:
            return "相机设备不可用"
        case .inputCreationFailed(let error):
            return "创建相机输入失败: \(error.localizedDescription)"
        case .outputConfigurationFailed:
            return "配置相机输出失败"
        case .sessionConfigurationFailed:
            return "配置相机会话失败"
        case .sessionStartFailed:
            return "启动相机会话失败"
        case .unknown(let error):
            return "未知相机错误: \(error.localizedDescription)"
        }
    }
}
