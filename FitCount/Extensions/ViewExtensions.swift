import SwiftUI
import UIKit
import AVFoundation

// MARK: - SwiftUI视图扩展

extension View {
    
    // MARK: - 屏幕方向适配
    
    /// 屏幕方向自适应修饰符
    /// - Parameter horizontalSizeClass: 水平尺寸类别
    /// - Returns: 修饰后的视图
    func adaptToScreenOrientation(horizontalSizeClass: UserInterfaceSizeClass?) -> some View {
        self.modifier(OrientationAdaptiveModifier(horizontalSizeClass: horizontalSizeClass))
    }
    
    // MARK: - 自定义圆角
    
    /// 自定义圆角修饰符 - 支持指定特定角落
    /// - Parameters:
    ///   - radius: 圆角半径
    ///   - corners: 要设置圆角的角落
    /// - Returns: 修饰后的视图
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
    
    // MARK: - 条件修饰符
    
    /// 条件修饰符 - 根据条件应用修饰符
    /// - Parameters:
    ///   - condition: 条件
    ///   - transform: 当条件为true时应用的变换
    /// - Returns: 修饰后的视图
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
    
    /// 可选条件修饰符 - 根据可选值应用修饰符
    /// - Parameters:
    ///   - value: 可选值
    ///   - transform: 当值不为nil时应用的变换
    /// - Returns: 修饰后的视图
    @ViewBuilder
    func ifLet<T, Content: View>(_ value: T?, transform: (Self, T) -> Content) -> some View {
        if let value = value {
            transform(self, value)
        } else {
            self
        }
    }
    
    // MARK: - 隐藏修饰符
    
    /// 隐藏视图修饰符
    /// - Parameter hidden: 是否隐藏
    /// - Returns: 修饰后的视图
    @ViewBuilder
    func hidden(_ hidden: Bool) -> some View {
        if hidden {
            self.hidden()
        } else {
            self
        }
    }
    
    // MARK: - 调试修饰符
    
    /// 调试边框修饰符 - 在调试模式下显示边框
    /// - Parameter color: 边框颜色，默认为红色
    /// - Returns: 修饰后的视图
    func debugBorder(_ color: Color = .red) -> some View {
        #if DEBUG
        return self.border(color, width: 1)
        #else
        return self
        #endif
    }
    
    /// 调试背景修饰符 - 在调试模式下显示背景色
    /// - Parameter color: 背景颜色，默认为半透明红色
    /// - Returns: 修饰后的视图
    func debugBackground(_ color: Color = Color.red.opacity(0.3)) -> some View {
        #if DEBUG
        return self.background(color)
        #else
        return self
        #endif
    }
}

// MARK: - 屏幕方向自适应修饰符

/// 用于处理屏幕方向变化的扩展视图修饰符
struct OrientationAdaptiveModifier: ViewModifier {
    let horizontalSizeClass: UserInterfaceSizeClass?
    
    func body(content: Content) -> some View {
        if horizontalSizeClass == .regular {
            // 横屏布局
            content
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else {
            // 竖屏布局
            content
        }
    }
}

// MARK: - 自定义圆角形状

/// 自定义圆角形状 - 支持指定特定角落为圆角
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners
    
    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// MARK: - 安全区域扩展

extension View {
    
    /// 忽略特定边缘的安全区域
    /// - Parameter edges: 要忽略的边缘
    /// - Returns: 修饰后的视图
    func ignoreSafeArea(_ edges: Edge.Set = .all) -> some View {
        self.edgesIgnoringSafeArea(edges)
    }
    
    /// 仅在特定条件下忽略安全区域
    /// - Parameters:
    ///   - condition: 条件
    ///   - edges: 要忽略的边缘
    /// - Returns: 修饰后的视图
    @ViewBuilder
    func ignoreSafeAreaIf(_ condition: Bool, _ edges: Edge.Set = .all) -> some View {
        if condition {
            self.edgesIgnoringSafeArea(edges)
        } else {
            self
        }
    }
}

// MARK: - 键盘响应扩展

extension View {
    
    /// 键盘出现时调整视图
    /// - Returns: 修饰后的视图
    func keyboardAdaptive() -> some View {
        self.modifier(KeyboardAdaptiveModifier())
    }
}

/// 键盘自适应修饰符
struct KeyboardAdaptiveModifier: ViewModifier {
    @State private var keyboardHeight: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .padding(.bottom, keyboardHeight)
            .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)) { notification in
                if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue {
                    keyboardHeight = keyboardFrame.cgRectValue.height
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)) { _ in
                keyboardHeight = 0
            }
    }
}

// MARK: - 动画扩展

extension View {
    
    /// 弹簧动画修饰符
    /// - Parameters:
    ///   - response: 响应时间
    ///   - dampingFraction: 阻尼系数
    ///   - blendDuration: 混合持续时间
    /// - Returns: 修饰后的视图
    func springAnimation(response: Double = 0.5, dampingFraction: Double = 0.8, blendDuration: Double = 0) -> some View {
        self.animation(.spring(response: response, dampingFraction: dampingFraction, blendDuration: blendDuration), value: UUID())
    }
    
    /// 缓动动画修饰符
    /// - Parameter duration: 动画持续时间
    /// - Returns: 修饰后的视图
    func easeAnimation(duration: Double = 0.3) -> some View {
        self.animation(.easeInOut(duration: duration), value: UUID())
    }
}

// MARK: - 触觉反馈扩展

extension View {
    
    /// 添加触觉反馈
    /// - Parameter style: 反馈样式
    /// - Returns: 修饰后的视图
    func hapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle = .medium) -> some View {
        self.onTapGesture {
            let impactFeedback = UIImpactFeedbackGenerator(style: style)
            impactFeedback.impactOccurred()
        }
    }
    
    /// 添加选择反馈
    /// - Returns: 修饰后的视图
    func selectionFeedback() -> some View {
        self.onTapGesture {
            let selectionFeedback = UISelectionFeedbackGenerator()
            selectionFeedback.selectionChanged()
        }
    }
    
    /// 添加通知反馈
    /// - Parameter type: 通知类型
    /// - Returns: 修饰后的视图
    func notificationFeedback(_ type: UINotificationFeedbackGenerator.FeedbackType = .success) -> some View {
        self.onTapGesture {
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(type)
        }
    }
}

// MARK: - AVLayerVideoGravity扩展

extension AVLayerVideoGravity {
    
    /// 转换为UIView.ContentMode
    var contentMode: UIView.ContentMode {
        switch self {
        case .resizeAspectFill:
            return .scaleAspectFill
        case .resizeAspect:
            return .scaleAspectFit
        case .resize:
            return .scaleToFill
        default:
            return .scaleAspectFill
        }
    }
}

// MARK: - UIImage.Orientation扩展

extension UIImage.Orientation {
    
    /// 从设备方向创建图像方向
    /// - Parameter deviceOrientation: 设备方向
    /// - Returns: 对应的图像方向
    static func from(deviceOrientation: UIDeviceOrientation) -> UIImage.Orientation {
        switch deviceOrientation {
        case .portrait:
            return .up
        case .portraitUpsideDown:
            return .down
        case .landscapeLeft:
            return .right
        case .landscapeRight:
            return .left
        default:
            return .up
        }
    }
}

// MARK: - 颜色扩展

extension Color {
    
    /// 从十六进制字符串创建颜色
    /// - Parameter hex: 十六进制颜色字符串（如："#FF0000" 或 "FF0000"）
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    /// 应用主题色
    static var primaryTheme: Color {
        return Color.blue
    }
    
    /// 应用次要主题色
    static var secondaryTheme: Color {
        return Color.gray
    }
    
    /// 应用强调色
    static var accentTheme: Color {
        return Color.orange
    }
}
