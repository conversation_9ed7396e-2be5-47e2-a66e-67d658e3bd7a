import Foundation
import AVFoundation
import Combine
import UIKit
import MediaPipeTasksVision

// MARK: - 姿态叠加管理器

/// 姿态叠加管理器
/// 负责管理姿态叠加视图的创建、更新和坐标转换
/// 作为相机服务和姿态检测服务之间的桥梁
final class PoseOverlayManager: ObservableObject, CameraDataOutputDelegate, PoseDetectionResultDelegate {
    
    // MARK: - 发布属性
    
    /// 姿态叠加视图
    @Published private(set) var overlayView: PoseOverlayUIView?
    
    /// 当前渲染样式
    @Published var renderingStyle: PoseRenderingStyle = .default {
        didSet {
            overlayView?.setRenderingStyle(renderingStyle)
        }
    }
    
    /// 是否启用叠加显示
    @Published var isOverlayEnabled: Bool = true {
        didSet {
            if !isOverlayEnabled {
                overlayView?.clearOverlay()
            }
        }
    }
    
    // MARK: - 私有属性
    
    /// 坐标转换器
    var coordinateTransformer: PoseCoordinateTransformerProtocol = DefaultPoseCoordinateTransformer()
    
    /// 当前相机图像尺寸
    private var currentImageSize: CGSize = CGSize(width: 640, height: 480)
    
    /// 当前视图尺寸
    private var currentViewSize: CGSize = .zero
    
    /// 当前内容模式
    private var currentContentMode: UIView.ContentMode = .scaleAspectFill
    
    /// 当前设备方向
    private var currentOrientation: UIImage.Orientation = .up
    
    /// 最后更新时间
    private var lastUpdateTime: TimeInterval = 0
    
    /// 更新频率限制（每秒最大更新次数）
    private let maxUpdatesPerSecond: Double = 30.0
    
    /// Combine取消令牌集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    init() {
        DebugLogger.info("PoseOverlayManager初始化")
        setupOverlayView()
    }
    
    deinit {
        DebugLogger.info("PoseOverlayManager销毁")
        clearOverlay()
    }
    
    // MARK: - 公共方法
    
    /// 设置叠加视图尺寸
    /// - Parameter size: 视图尺寸
    func setOverlayViewSize(_ size: CGSize) {
        currentViewSize = size
        DebugLogger.verbose("设置叠加视图尺寸: \(size)")
    }
    
    /// 设置内容模式
    /// - Parameter contentMode: 内容模式
    func setContentMode(_ contentMode: UIView.ContentMode) {
        currentContentMode = contentMode
        DebugLogger.verbose("设置内容模式: \(contentMode)")
    }
    
    /// 清除叠加内容
    func clearOverlay() {
        DebugLogger.debug("清除姿态叠加内容")
        overlayView?.clearOverlay()
    }
    
    /// 更新渲染样式
    /// - Parameter style: 新的渲染样式
    func updateRenderingStyle(_ style: PoseRenderingStyle) {
        renderingStyle = style
    }
    
    // MARK: - CameraDataOutputDelegate实现
    
    /// 相机输出新的视频帧
    /// - Parameters:
    ///   - sampleBuffer: 视频帧缓冲区
    ///   - orientation: 设备方向
    func cameraDidOutput(sampleBuffer: CMSampleBuffer, orientation: UIImage.Orientation) {
        // 更新当前方向
        currentOrientation = orientation
        
        // 更新图像尺寸
        if let imageBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) {
            let width = CVPixelBufferGetWidth(imageBuffer)
            let height = CVPixelBufferGetHeight(imageBuffer)
            currentImageSize = CGSize(width: width, height: height)
        }
        
        // 这里不直接处理视频帧，而是等待姿态检测结果
        // 姿态检测服务会异步处理这个视频帧并通过delegate回调结果
    }
    
    /// 相机会话遇到运行时错误
    /// - Parameter error: 错误信息
    func cameraDidEncounterError(_ error: Error) {
        DebugLogger.error("相机错误，清除叠加内容: \(error.localizedDescription)")
        clearOverlay()
    }
    
    /// 相机会话被中断
    /// - Parameter reason: 中断原因
    func cameraSessionWasInterrupted(reason: String) {
        DebugLogger.warning("相机会话被中断，清除叠加内容: \(reason)")
        clearOverlay()
    }
    
    /// 相机会话中断结束
    func cameraSessionInterruptionEnded() {
        DebugLogger.info("相机会话中断结束")
        // 可以在这里重新初始化叠加视图
    }
    
    // MARK: - PoseDetectionResultDelegate实现
    
    /// 检测完成回调
    /// - Parameters:
    ///   - result: 检测结果，可能为nil
    ///   - error: 检测过程中的错误，可能为nil
    func poseDetectionDidComplete(result: PoseDetectionResult?, error: Error?) {
        if let error = error {
            DebugLogger.error("姿态检测错误: \(error.localizedDescription)")
            return
        }
        
        guard let result = result, isOverlayEnabled else {
            return
        }
        
        // 频率限制检查
        let currentTime = Date().timeIntervalSince1970
        if currentTime - lastUpdateTime < (1.0 / maxUpdatesPerSecond) {
            return
        }
        lastUpdateTime = currentTime
        
        // 更新叠加显示
        updateOverlayWithDetectionResult(result)
    }
    
    /// 实时检测结果更新
    /// - Parameter result: 最新的检测结果
    func poseDetectionDidUpdate(result: PoseDetectionResult) {
        guard isOverlayEnabled else { return }
        updateOverlayWithDetectionResult(result)
    }
    
    /// 检测状态变化
    /// - Parameter isDetecting: 是否正在检测
    func poseDetectionStateDidChange(isDetecting: Bool) {
        DebugLogger.debug("姿态检测状态变化: \(isDetecting ? "开始" : "停止")")
        
        if !isDetecting {
            // 检测停止时清除叠加内容
            clearOverlay()
        }
    }
    
    // MARK: - 私有方法
    
    /// 设置叠加视图
    private func setupOverlayView() {
        overlayView = PoseOverlayViewFactory.createDefaultOverlayView()
        overlayView?.setRenderingStyle(renderingStyle)
        DebugLogger.debug("创建姿态叠加视图")
    }
    
    /// 使用检测结果更新叠加显示
    /// - Parameter result: 检测结果
    private func updateOverlayWithDetectionResult(_ result: PoseDetectionResult) {
        guard let overlayView = overlayView,
              currentViewSize != .zero else {
            return
        }
        
        // 使用坐标转换器创建叠加数据
        let overlayData = coordinateTransformer.createOverlayData(
            from: result.landmarks,
            imageSize: currentImageSize,
            viewSize: currentViewSize,
            contentMode: currentContentMode,
            orientation: currentOrientation
        )
        
        // 在主线程更新UI
        DispatchQueue.main.async {
            overlayView.updateOverlay(
                overlayData: overlayData,
                imageSize: self.currentImageSize,
                viewSize: self.currentViewSize,
                contentMode: self.currentContentMode
            )
        }
    }
}

// MARK: - 叠加管理器配置

/// 姿态叠加管理器配置
/// 用于配置叠加管理器的行为参数
struct PoseOverlayManagerConfiguration {
    /// 最大更新频率（每秒）
    let maxUpdatesPerSecond: Double
    /// 默认渲染样式
    let defaultRenderingStyle: PoseRenderingStyle
    /// 是否默认启用叠加
    let defaultOverlayEnabled: Bool
    /// 坐标转换器类型
    let coordinateTransformerType: PoseCoordinateTransformerProtocol.Type
    
    /// 初始化配置
    /// - Parameters:
    ///   - maxUpdatesPerSecond: 最大更新频率，默认为30
    ///   - defaultRenderingStyle: 默认渲染样式
    ///   - defaultOverlayEnabled: 是否默认启用叠加，默认为true
    ///   - coordinateTransformerType: 坐标转换器类型
    init(maxUpdatesPerSecond: Double = 30.0,
         defaultRenderingStyle: PoseRenderingStyle = .default,
         defaultOverlayEnabled: Bool = true,
         coordinateTransformerType: PoseCoordinateTransformerProtocol.Type = DefaultPoseCoordinateTransformer.self) {
        self.maxUpdatesPerSecond = maxUpdatesPerSecond
        self.defaultRenderingStyle = defaultRenderingStyle
        self.defaultOverlayEnabled = defaultOverlayEnabled
        self.coordinateTransformerType = coordinateTransformerType
    }
    
    /// 默认配置
    static var `default`: PoseOverlayManagerConfiguration {
        return PoseOverlayManagerConfiguration()
    }
    
    /// 高性能配置（降低更新频率）
    static var highPerformance: PoseOverlayManagerConfiguration {
        return PoseOverlayManagerConfiguration(
            maxUpdatesPerSecond: 15.0,
            defaultRenderingStyle: .minimal
        )
    }
    
    /// 高质量配置（提高更新频率）
    static var highQuality: PoseOverlayManagerConfiguration {
        return PoseOverlayManagerConfiguration(
            maxUpdatesPerSecond: 60.0,
            defaultRenderingStyle: .default
        )
    }
}

// MARK: - 渲染样式扩展

extension PoseRenderingStyle {
    
    /// 简约样式（只显示连接线）
    static var minimal: PoseRenderingStyle {
        return PoseRenderingStyle(
            pointColor: .clear,
            pointFillColor: .clear,
            pointRadius: 0.0,
            lineColor: .white,
            lineWidth: 1.0,
            showPoints: false,
            showLines: true,
            alpha: 0.7
        )
    }
    
    /// 高对比度样式
    static var highContrast: PoseRenderingStyle {
        return PoseRenderingStyle(
            pointColor: .white,
            pointFillColor: .red,
            pointRadius: 3.0,
            lineColor: .cyan,
            lineWidth: 3.0,
            showPoints: true,
            showLines: true,
            alpha: 0.9
        )
    }
    
    /// 调试样式（显示所有元素）
    static var debug: PoseRenderingStyle {
        return PoseRenderingStyle(
            pointColor: .yellow,
            pointFillColor: .red,
            pointRadius: 4.0,
            lineColor: .green,
            lineWidth: 2.0,
            showPoints: true,
            showLines: true,
            alpha: 1.0
        )
    }
}
