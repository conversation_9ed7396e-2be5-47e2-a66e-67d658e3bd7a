import SwiftUI
import AVFoundation
import UIKit

// MARK: - 相机预览视图组件

/// 相机预览UIView实现
/// 负责显示AVCaptureVideoPreviewLayer的自定义UIView
class CameraPreviewUIView: UIView, CameraPreviewViewProtocol {
    
    // MARK: - 属性
    
    /// 预览层引用
    var previewLayer: AVCaptureVideoPreviewLayer? {
        didSet {
            DebugLogger.debug("设置相机预览层")
            updatePreviewLayer(from: oldValue, to: previewLayer)
        }
    }
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    // MARK: - 视图生命周期
    
    override func layoutSubviews() {
        super.layoutSubviews()
        DebugLogger.verbose("CameraPreviewUIView.layoutSubviews 被调用，bounds=\(bounds)")
        updatePreviewFrame(bounds)
    }
    
    override func willMove(toSuperview newSuperview: UIView?) {
        super.willMove(toSuperview: newSuperview)
        
        if newSuperview == nil {
            DebugLogger.debug("CameraPreviewUIView将从视图层次结构中移除")
        } else {
            DebugLogger.debug("CameraPreviewUIView将添加到新的父视图")
        }
    }
    
    // MARK: - CameraPreviewViewProtocol实现
    
    /// 设置预览层
    /// - Parameter previewLayer: AVCaptureVideoPreviewLayer实例
    func setPreviewLayer(_ previewLayer: AVCaptureVideoPreviewLayer?) {
        self.previewLayer = previewLayer
    }
    
    /// 更新预览层框架
    /// - Parameter frame: 新的框架尺寸
    func updatePreviewFrame(_ frame: CGRect) {
        CATransaction.begin()
        CATransaction.setDisableActions(true) // 禁用动画
        previewLayer?.frame = frame
        CATransaction.commit()
        
        // 确保预览层仍在视图层次结构中
        if let layer = previewLayer, layer.superlayer == nil {
            DebugLogger.warning("预览层已从层次结构中移除，重新添加")
            self.layer.addSublayer(layer)
        }
    }
    
    /// 清除预览内容
    func clearPreview() {
        DebugLogger.debug("清除相机预览内容")
        previewLayer?.removeFromSuperlayer()
        previewLayer = nil
    }
    
    // MARK: - 私有方法
    
    /// 设置视图基本属性
    private func setupView() {
        backgroundColor = .black
        clipsToBounds = true
    }
    
    /// 更新预览层
    /// - Parameters:
    ///   - oldLayer: 旧的预览层
    ///   - newLayer: 新的预览层
    private func updatePreviewLayer(from oldLayer: AVCaptureVideoPreviewLayer?, to newLayer: AVCaptureVideoPreviewLayer?) {
        // 移除旧层
        if let oldLayer = oldLayer {
            DebugLogger.debug("移除旧的预览层")
            oldLayer.removeFromSuperlayer()
        }
        
        // 设置新层
        if let newLayer = newLayer {
            DebugLogger.debug("添加新的预览层到视图")
            
            // 配置预览层属性
            newLayer.videoGravity = .resizeAspectFill
            layer.addSublayer(newLayer)
            
            // 设置预览层框架以填充视图
            updatePreviewFrame(bounds)
            
            // 检查预览层会话是否设置
            validatePreviewLayerSetup(newLayer)
        } else {
            DebugLogger.debug("预览层被设置为nil")
        }
    }
    
    /// 验证预览层设置
    /// - Parameter previewLayer: 要验证的预览层
    private func validatePreviewLayerSetup(_ previewLayer: AVCaptureVideoPreviewLayer) {
        if let session = previewLayer.session {
            DebugLogger.debug("预览层关联的会话存在")
            
            // 检查会话是否正在运行
            if session.isRunning {
                DebugLogger.debug("预览层关联的会话正在运行")
            } else {
                DebugLogger.warning("预览层关联的会话未运行")
            }
        } else {
            DebugLogger.error("预览层没有关联会话！")
        }
        
        // 检查连接状态
        if let connection = previewLayer.connection {
            DebugLogger.debug("预览层连接状态: enabled=\(connection.isEnabled)")
            
            // 尝试启用连接
            connection.isEnabled = true
        } else {
            DebugLogger.warning("预览层没有连接")
        }
    }
}

// MARK: - SwiftUI包装器

/// SwiftUI相机预览视图包装器
/// 将UIKit的CameraPreviewUIView包装为SwiftUI视图
struct CameraPreviewView: UIViewRepresentable {
    
    /// 预览层
    let previewLayer: AVCaptureVideoPreviewLayer
    
    // MARK: - UIViewRepresentable实现
    
    /// 创建UIView
    /// - Parameter context: 上下文
    /// - Returns: 配置好的CameraPreviewUIView
    func makeUIView(context: Context) -> CameraPreviewUIView {
        DebugLogger.debug("CameraPreviewView.makeUIView被调用")
        
        let view = CameraPreviewUIView()
        view.setPreviewLayer(previewLayer)
        
        // 检查关联的会话
        DebugLogger.debug("预览层会话状态: \(previewLayer.session?.isRunning == true ? "正在运行" : "未运行")")
        
        return view
    }
    
    /// 更新UIView
    /// - Parameters:
    ///   - uiView: 要更新的视图
    ///   - context: 上下文
    func updateUIView(_ uiView: CameraPreviewUIView, context: Context) {
        DebugLogger.debug("CameraPreviewView.updateUIView被调用")
        
        // 检查预览层是否相同
        if uiView.previewLayer !== previewLayer {
            DebugLogger.debug("预览层实例已更改，更新UIView")
            uiView.setPreviewLayer(previewLayer)
        } else {
            DebugLogger.verbose("预览层实例相同，无需更新")
        }
        
        // 无论如何都重新布局视图
        uiView.setNeedsLayout()
    }
    
    /// 视图将从层次结构中移除时调用
    /// - Parameters:
    ///   - uiView: 要销毁的视图
    ///   - coordinator: 协调器
    static func dismantleUIView(_ uiView: CameraPreviewUIView, coordinator: ()) {
        DebugLogger.debug("CameraPreviewView.dismantleUIView被调用")
        uiView.clearPreview()
    }
}

// MARK: - 预览视图修饰符

extension View {
    
    /// 添加相机预览背景
    /// - Parameter previewLayer: 预览层，可选
    /// - Returns: 修饰后的视图
    func cameraPreviewBackground(_ previewLayer: AVCaptureVideoPreviewLayer?) -> some View {
        ZStack {
            if let previewLayer = previewLayer {
                CameraPreviewView(previewLayer: previewLayer)
                    .edgesIgnoringSafeArea(.all)
            } else {
                Color.black
                    .edgesIgnoringSafeArea(.all)
            }
            
            self
        }
    }
}

// MARK: - 预览视图配置

/// 相机预览视图配置
/// 用于配置预览视图的显示参数
struct CameraPreviewConfiguration {
    /// 视频重力模式
    let videoGravity: AVLayerVideoGravity
    /// 背景颜色
    let backgroundColor: UIColor
    /// 是否启用裁剪
    let clipsToBounds: Bool
    
    /// 初始化配置
    /// - Parameters:
    ///   - videoGravity: 视频重力模式，默认为填充
    ///   - backgroundColor: 背景颜色，默认为黑色
    ///   - clipsToBounds: 是否启用裁剪，默认为true
    init(videoGravity: AVLayerVideoGravity = .resizeAspectFill,
         backgroundColor: UIColor = .black,
         clipsToBounds: Bool = true) {
        self.videoGravity = videoGravity
        self.backgroundColor = backgroundColor
        self.clipsToBounds = clipsToBounds
    }
    
    /// 默认配置
    static var `default`: CameraPreviewConfiguration {
        return CameraPreviewConfiguration()
    }
}

// MARK: - 预览视图状态

/// 相机预览视图状态
/// 表示预览视图的当前状态
enum CameraPreviewState {
    /// 未初始化
    case notInitialized
    /// 正在加载
    case loading
    /// 显示中
    case displaying
    /// 错误状态
    case error(String)
    
    /// 获取状态描述
    var description: String {
        switch self {
        case .notInitialized:
            return "未初始化"
        case .loading:
            return "正在加载..."
        case .displaying:
            return "显示中"
        case .error(let message):
            return "错误: \(message)"
        }
    }
}
