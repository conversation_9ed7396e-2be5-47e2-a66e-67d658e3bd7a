import SwiftUI
import UIKit
import CoreGraphics

// MARK: - 人体姿态叠加视图组件

/// 人体姿态叠加UIView实现
/// 负责在相机预览上绘制人体关键点和连接线的自定义UIView
class PoseOverlayUIView: UIView, PoseOverlayRendererProtocol {

    // MARK: - 属性

    /// 姿态叠加数据
    var poseOverlayData: FitCount.PoseOverlayData? {
        didSet {
            setNeedsDisplay()
        }
    }

    /// 内容图像尺寸
    private var contentImageSize: CGSize = CGSize.zero

    /// 图像内容模式
    var imageContentMode: UIView.ContentMode = .scaleAspectFit

    /// 设备方向
    private var orientation = UIDeviceOrientation.portrait

    /// 渲染样式
    private var renderingStyle: PoseRenderingStyle = .default

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    // MARK: - 视图设置

    /// 设置视图基本属性
    private func setupView() {
        backgroundColor = UIColor.clear
        isOpaque = false
        isUserInteractionEnabled = false // 不拦截触摸事件
    }

    // MARK: - PoseOverlayRendererProtocol实现

    /// 更新姿态叠加数据
    /// - Parameters:
    ///   - overlayData: 叠加数据
    ///   - imageSize: 原始图像尺寸
    ///   - viewSize: 视图尺寸
    ///   - contentMode: 内容模式
    func updateOverlay(overlayData: FitCount.PoseOverlayData?, imageSize: CGSize, viewSize: CGSize, contentMode: UIView.ContentMode) {
        DebugLogger.verbose("更新姿态叠加数据")

        self.contentImageSize = imageSize
        self.poseOverlayData = overlayData
        self.imageContentMode = contentMode
        self.orientation = UIDevice.current.orientation

        setNeedsDisplay()
    }

    /// 清除叠加内容
    func clearOverlay() {
        DebugLogger.debug("清除姿态叠加内容")

        poseOverlayData = nil
        contentImageSize = CGSize.zero
        imageContentMode = .scaleAspectFit
        orientation = UIDevice.current.orientation

        setNeedsDisplay()
    }

    /// 设置渲染样式
    /// - Parameter style: 渲染样式配置
    func setRenderingStyle(_ style: PoseRenderingStyle) {
        DebugLogger.debug("设置姿态渲染样式")
        renderingStyle = style
        setNeedsDisplay()
    }

    // MARK: - 绘制方法

    override func draw(_ rect: CGRect) {
        guard let overlayData = poseOverlayData else { return }

        // 设置透明度
        alpha = renderingStyle.alpha

        // 绘制连接线
        if renderingStyle.showLines {
            drawLines(overlayData.lines)
        }

        // 绘制关键点
        if renderingStyle.showPoints {
            drawDots(overlayData.dots)
        }
    }

    /// 绘制关键点
    /// - Parameter dots: 关键点坐标数组
    private func drawDots(_ dots: [CGPoint]) {
        for dot in dots {
            let dotRect = CGRect(
                x: dot.x - renderingStyle.pointRadius / 2,
                y: dot.y - renderingStyle.pointRadius / 2,
                width: renderingStyle.pointRadius,
                height: renderingStyle.pointRadius
            )

            let path = UIBezierPath(ovalIn: dotRect)
            renderingStyle.pointFillColor.setFill()
            renderingStyle.pointColor.setStroke()
            path.lineWidth = 1.0
            path.stroke()
            path.fill()
        }
    }

    /// 绘制连接线
    /// - Parameter lines: 连接线数组
    private func drawLines(_ lines: [FitCount.PoseLine]) {
        let path = UIBezierPath()

        for line in lines {
            path.move(to: line.from)
            path.addLine(to: line.to)
        }

        path.lineWidth = renderingStyle.lineWidth
        renderingStyle.lineColor.setStroke()
        path.stroke()
    }
}

// MARK: - SwiftUI包装器

/// SwiftUI人体姿态叠加视图包装器
/// 将UIKit的PoseOverlayUIView包装为SwiftUI视图
struct PoseOverlayView: UIViewRepresentable {

    /// 姿态叠加UIView实例
    let poseOverlayView: PoseOverlayUIView

    /// 渲染样式
    let renderingStyle: PoseRenderingStyle

    /// 初始化
    /// - Parameters:
    ///   - poseOverlayView: 姿态叠加UIView实例
    ///   - renderingStyle: 渲染样式，默认使用默认样式
    init(poseOverlayView: PoseOverlayUIView, renderingStyle: PoseRenderingStyle = .default) {
        self.poseOverlayView = poseOverlayView
        self.renderingStyle = renderingStyle
    }

    // MARK: - UIViewRepresentable实现

    /// 创建UIView
    /// - Parameter context: 上下文
    /// - Returns: 配置好的PoseOverlayUIView
    func makeUIView(context: Context) -> PoseOverlayUIView {
        DebugLogger.debug("PoseOverlayView.makeUIView被调用")

        poseOverlayView.setRenderingStyle(renderingStyle)
        return poseOverlayView
    }

    /// 更新UIView
    /// - Parameters:
    ///   - uiView: 要更新的视图
    ///   - context: 上下文
    func updateUIView(_ uiView: PoseOverlayUIView, context: Context) {
        DebugLogger.verbose("PoseOverlayView.updateUIView被调用")

        // 更新渲染样式
        uiView.setRenderingStyle(renderingStyle)
    }

    /// 视图将从层次结构中移除时调用
    /// - Parameters:
    ///   - uiView: 要销毁的视图
    ///   - coordinator: 协调器
    static func dismantleUIView(_ uiView: PoseOverlayUIView, coordinator: ()) {
        DebugLogger.debug("PoseOverlayView.dismantleUIView被调用")
        uiView.clearOverlay()
    }
}

// MARK: - 姿态叠加视图工厂

/// 姿态叠加视图工厂
/// 负责创建和配置姿态叠加视图
class PoseOverlayViewFactory {

    /// 创建默认的姿态叠加视图
    /// - Returns: 配置好的姿态叠加UIView
    static func createDefaultOverlayView() -> PoseOverlayUIView {
        let overlayView = PoseOverlayUIView()
        overlayView.setRenderingStyle(.default)
        return overlayView
    }

    /// 创建自定义样式的姿态叠加视图
    /// - Parameter style: 渲染样式
    /// - Returns: 配置好的姿态叠加UIView
    static func createOverlayView(with style: PoseRenderingStyle) -> PoseOverlayUIView {
        let overlayView = PoseOverlayUIView()
        overlayView.setRenderingStyle(style)
        return overlayView
    }

    /// 创建高对比度样式的姿态叠加视图
    /// - Returns: 配置好的姿态叠加UIView
    static func createHighContrastOverlayView() -> PoseOverlayUIView {
        let style = PoseRenderingStyle(
            pointColor: .white,
            pointFillColor: .red,
            pointRadius: 3.0,
            lineColor: .cyan,
            lineWidth: 3.0,
            showPoints: true,
            showLines: true,
            alpha: 0.9
        )

        return createOverlayView(with: style)
    }

    /// 创建简约样式的姿态叠加视图
    /// - Returns: 配置好的姿态叠加UIView
    static func createMinimalOverlayView() -> PoseOverlayUIView {
        let style = PoseRenderingStyle(
            pointColor: .clear,
            pointFillColor: .clear,
            pointRadius: 0.0,
            lineColor: .white,
            lineWidth: 1.0,
            showPoints: false,
            showLines: true,
            alpha: 0.7
        )

        return createOverlayView(with: style)
    }
}

// MARK: - 姿态叠加视图修饰符

extension View {

    /// 添加姿态叠加层
    /// - Parameters:
    ///   - overlayView: 姿态叠加视图
    ///   - style: 渲染样式
    /// - Returns: 修饰后的视图
    func poseOverlay(_ overlayView: PoseOverlayUIView?, style: PoseRenderingStyle = .default) -> some View {
        ZStack {
            self

            if let overlayView = overlayView {
                PoseOverlayView(poseOverlayView: overlayView, renderingStyle: style)
                    .allowsHitTesting(false) // 不拦截触摸事件
            }
        }
    }
}

// MARK: - 姿态叠加数据扩展

extension FitCount.PoseOverlayData {

    /// 检查数据是否有效
    var isValid: Bool {
        return !dots.isEmpty || !lines.isEmpty
    }

    /// 获取边界矩形
    var boundingRect: CGRect {
        guard !dots.isEmpty else { return .zero }

        let xValues = dots.map { $0.x }
        let yValues = dots.map { $0.y }

        let minX = xValues.min() ?? 0
        let maxX = xValues.max() ?? 0
        let minY = yValues.min() ?? 0
        let maxY = yValues.max() ?? 0

        return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
    }

    /// 缩放叠加数据
    /// - Parameter scale: 缩放因子
    /// - Returns: 缩放后的叠加数据
    func scaled(by scale: CGFloat) -> FitCount.PoseOverlayData {
        let scaledDots = dots.map { CGPoint(x: $0.x * scale, y: $0.y * scale) }
        let scaledLines = lines.map { FitCount.PoseLine(from: CGPoint(x: $0.from.x * scale, y: $0.from.y * scale),
                                              to: CGPoint(x: $0.to.x * scale, y: $0.to.y * scale)) }

        return FitCount.PoseOverlayData(dots: scaledDots, lines: scaledLines)
    }

    /// 平移叠加数据
    /// - Parameter offset: 平移偏移量
    /// - Returns: 平移后的叠加数据
    func translated(by offset: CGPoint) -> FitCount.PoseOverlayData {
        let translatedDots = dots.map { CGPoint(x: $0.x + offset.x, y: $0.y + offset.y) }
        let translatedLines = lines.map { FitCount.PoseLine(from: CGPoint(x: $0.from.x + offset.x, y: $0.from.y + offset.y),
                                                  to: CGPoint(x: $0.to.x + offset.x, y: $0.to.y + offset.y)) }

        return FitCount.PoseOverlayData(dots: translatedDots, lines: translatedLines)
    }
}
